<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Recognition Attendance System</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/modern-ui.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/animations.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/fast-response.css') }}" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid position-relative">
            <a class="navbar-brand ms-4" href="{{ url_for('index') }}" data-aos="fade-right">
                <i class="fas fa-camera-retro animate-pulse"></i>
                <span>Face Recognition Attendance</span>
            </a>

            <!-- Navbar items -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-2"></i> Home
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield me-2"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li>
                                <a class="dropdown-item" href="{{ url_for('login') }}">
                                    <i class="fas fa-sign-in-alt me-2"></i> Login
                                </a>
                            </li>
                            <li>
                                <form action="{{ url_for('lock') }}" method="POST" style="margin: 0;">
                                    <button type="submit" class="dropdown-item" style="color: var(--danger-color);">
                                        <i class="fas fa-power-off me-2"></i> Shut Down
                                    </button>
                                </form>
                            </li>
                        </ul>
                    </li>
                </ul>
            </div>

            <!-- Mobile toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
        </div>
    </nav>

    <!-- Flash Messages -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message alert alert-{{ category }} animate-fade-in-right">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle animate-success-check"></i>
                        {% elif category == 'danger' %}
                            <i class="fas fa-times-circle animate-shake"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle animate-pulse"></i>
                        {% elif category == 'info' %}
                            <i class="fas fa-info-circle animate-fade-in"></i>
                        {% endif %}
                        <span>{{ message }}</span>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AOS Animation Library -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Sleep Timer JS -->
    <script src="{{ url_for('static', filename='js/sleep-timer.js') }}"></script>
    <!-- Custom JS -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
            once: true,
            offset: 50,
            delay: 100
        });

        // Optimized flash message auto-hide
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelectorAll('.flash-message').forEach(function(element) {
                    element.style.opacity = '0';
                    element.style.transform = 'translateX(50px)';
                    setTimeout(function() {
                        element.remove();
                    }, 200); // Faster removal
                });
            }, 1200); // Faster auto-hide

            // Add smooth page transitions
            document.body.classList.add('animate-fade-in');

            // Optimized button click handling for better responsiveness
            document.addEventListener('click', function(e) {
                const button = e.target.closest('.btn');
                if (!button ||
                    button.classList.contains('no-spinner') ||
                    button.classList.contains('disabled') ||
                    button.hasAttribute('data-bs-toggle') ||
                    button.getAttribute('type') === 'button' ||
                    button.closest('.dropdown-menu')) {
                    return;
                }

                // Immediate visual feedback for better responsiveness
                button.style.opacity = '0.8';
                button.style.transform = 'scale(0.98)';

                // Only add spinner for actual form submissions
                if (button.type === 'submit' || (button.tagName === 'A' && button.href)) {
                    // Minimal spinner after short delay to avoid blocking UI
                    setTimeout(() => {
                        if (!button.querySelector('.quick-spinner')) {
                            const spinner = document.createElement('span');
                            spinner.className = 'quick-spinner ms-1';
                            spinner.innerHTML = '⏳';
                            spinner.style.fontSize = '14px';
                            button.appendChild(spinner);
                        }
                    }, 50);
                }
            }, true);
        });
    </script>
</body>
</html>
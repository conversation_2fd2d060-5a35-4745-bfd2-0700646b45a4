<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - Face Recognition Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-ui.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/animations.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #1e293b;
            display: flex;
            flex-direction: column;
        }
        .admin-panel-card {
            background: #fff;
            border-radius: 2rem;
            box-shadow: 0 10px 32px rgba(37,99,235,0.10), 0 2px 8px rgba(0,0,0,0.08);
            border: 1.5px solid #e5e7eb;
            padding: 2.5rem 2.5rem 2rem 2.5rem;
            max-width: 700px;
            width: 100%;
            margin: 3rem auto 0 auto;
            animation: fadeInCard 0.7s cubic-bezier(.4,1.4,.6,1);
        }
        @keyframes fadeInCard {
            from { opacity: 0; transform: translateY(30px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .admin-title {
            font-size: 2.5rem;
            font-weight: 800;
            letter-spacing: -1px;
            margin-bottom: 1.5rem;
            background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .admin-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
        }
        .admin-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.7rem;
            padding: 1.1rem 1.2rem;
            border-radius: 1.2rem;
            font-size: 1.15rem;
            font-weight: 700;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: #fff;
            border: none;
            box-shadow: 0 2px 8px rgba(37,99,235,0.08);
            transition: all 0.18s cubic-bezier(.4,1.4,.6,1);
            position: relative;
            overflow: hidden;
            text-decoration: none;
        }
        .admin-btn:hover, .admin-btn:focus {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 6px 16px rgba(37,99,235,0.12);
        }
        .admin-btn.btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: #fff;
        }
        .admin-btn.btn-danger:hover, .admin-btn.btn-danger:focus {
            background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
        }
        .admin-section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2563eb;
            margin-bottom: 0.7rem;
            margin-top: 2.2rem;
        }
        @media (max-width: 700px) {
            .admin-panel-card { padding: 1.2rem 0.5rem 1.2rem 0.5rem; }
            .admin-title { font-size: 2rem; }
            .admin-actions { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message alert alert-{{ category }} animate-fade-in-right">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle animate-success-check"></i>
                        {% elif category == 'danger' %}
                            <i class="fas fa-times-circle animate-shake"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle animate-pulse"></i>
                        {% elif category == 'info' %}
                            <i class="fas fa-info-circle animate-fade-in"></i>
                        {% endif %}
                        <span>{{ message }}</span>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="admin-panel-card" data-aos="zoom-in">
        <div class="text-center">
            <div class="admin-title" data-aos="fade-down">Admin Panel</div>
        </div>
        <div class="admin-actions" data-aos="fade-up" data-aos-delay="100">
            <a href="{{ url_for('list_students') }}" class="admin-btn">
                <i class="fas fa-users"></i> Student Management
            </a>
            <a href="{{ url_for('dashboard') }}" class="admin-btn">
                <i class="fas fa-clipboard-list"></i> Attendance Dashboard
            </a>
            <a href="{{ url_for('reports_analytics') }}" class="admin-btn">
                <i class="fas fa-chart-bar"></i> Reports & Analytics
            </a>
            <a href="{{ url_for('system_settings') }}" class="admin-btn">
                <i class="fas fa-cogs"></i> System Settings
            </a>
            <a href="{{ url_for('system_monitoring') }}" class="admin-btn">
                <i class="fas fa-desktop"></i> System Monitoring
            </a>
            <a href="{{ url_for('user_management') }}" class="admin-btn">
                <i class="fas fa-user-shield"></i> User Management
            </a>
            <a href="{{ url_for('backup_security') }}" class="admin-btn">
                <i class="fas fa-shield-alt"></i> Backup & Security
            </a>
            <a href="{{ url_for('sleep_mode') }}" class="admin-btn">
                <i class="fas fa-clock"></i> Sleep Timer
            </a>
            <a href="{{ url_for('index') }}" class="admin-btn btn-danger">
                <i class="fas fa-sign-out-alt me-2"></i> Exit
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <!-- Sleep Timer JS -->
    <script src="{{ url_for('static', filename='js/sleep-timer.js') }}"></script>
    <script>
        AOS.init({
            duration: 800,
            easing: 'ease-out',
            once: true
        });

        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.querySelectorAll('.flash-message').forEach(function(element) {
                    element.style.opacity = '0';
                    element.style.transform = 'translateX(100px)';
                    setTimeout(function() {
                        element.remove();
                    }, 500);
                });
            }, 1500);

            document.body.classList.add('animate-fade-in');
        });
    </script>
</body>
</html>
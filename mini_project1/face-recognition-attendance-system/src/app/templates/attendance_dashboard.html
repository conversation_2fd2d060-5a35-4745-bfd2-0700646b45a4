{% extends "base.html" %}

{% block content %}
<style>
    body {
        background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
        min-height: 100vh;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    }
    .modern-card {
        background: white;
        border-radius: 1.5rem;
        box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 2px 8px rgba(0,0,0,0.08);
        padding: 2.5rem 2rem 2rem 2rem;
        max-width: 950px;
        margin: 2rem auto;
        animation: fadeInCard 0.7s cubic-bezier(.4,1.4,.6,1);
    }
    @keyframes fadeInCard {
        from { opacity: 0; transform: translateY(30px) scale(0.98); }
        to { opacity: 1; transform: translateY(0) scale(1); }
    }
    .modern-header {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem 2rem 1rem 2rem;
        margin: -2.5rem -2rem 2rem -2rem;
        box-shadow: 0 4px 16px rgba(37,99,235,0.08);
        font-size: 2.2rem;
        font-weight: 700;
        letter-spacing: -0.02em;
        text-shadow: 0 2px 8px rgba(37,99,235,0.10);
        animation: slideDown 0.7s cubic-bezier(.4,1.4,.6,1);
    }
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-30px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .modern-search {
        background: #f8faff;
        border-radius: 1rem;
        padding: 1.2rem 1.5rem;
        margin-bottom: 2rem;
        display: flex;
        gap: 1rem;
        align-items: center;
        box-shadow: 0 4px 16px rgba(37,99,235,0.06);
        animation: fadeInCard 1.1s cubic-bezier(.4,1.4,.6,1);
        border: 1px solid rgba(37,99,235,0.08);
    }
    .modern-search input[type="text"] {
        border-radius: 0.7rem;
        border: 1.5px solid #d1d5db;
        padding: 0.85rem 1.2rem;
        font-size: 1.1rem;
        transition: all 0.2s ease;
        flex: 1;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        color: #1f2937;
    }
    .modern-search input[type="text"]:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37,99,235,0.10);
        outline: none;
    }
    .modern-search button {
        border-radius: 0.7rem;
        font-size: 1.1rem;
        padding: 0.85rem 1.8rem;
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border: none;
        transition: all 0.2s ease;
        box-shadow: 0 4px 12px rgba(37,99,235,0.15);
        font-weight: 600;
        min-width: 120px;
        text-decoration: none;
    }
    .modern-search button:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }
    .modern-filter-bar {
        background: #f8faff;
        border-radius: 1rem;
        padding: 1.8rem 1.5rem 1.5rem 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 16px rgba(37,99,235,0.06);
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        animation: fadeInCard 1.2s cubic-bezier(.4,1.4,.6,1);
        border: 1px solid rgba(37,99,235,0.08);
    }
    .modern-filter-bar label {
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        display: block;
    }
    .modern-filter-bar select, .modern-filter-bar input[type="date"] {
        border-radius: 0.7rem;
        border: 1.5px solid #d1d5db;
        padding: 0.8rem 1.2rem;
        font-size: 1.1rem;
        transition: all 0.2s ease;
        width: 100%;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        color: #1f2937;
    }
    .modern-filter-bar select:focus, .modern-filter-bar input[type="date"]:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37,99,235,0.10);
        outline: none;
    }
    .modern-filter-bar .btn {
        width: 100%;
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        font-weight: 600;
        border-radius: 0.7rem;
        font-size: 1.1rem;
        margin-top: 0.5rem;
        transition: all 0.2s ease;
        padding: 0.8rem 1.2rem;
        border: none;
        box-shadow: 0 4px 12px rgba(37,99,235,0.15);
        text-decoration: none;
    }
    .modern-filter-bar .btn:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }
    .modern-table-container {
        border-radius: 1rem;
        overflow: hidden;
        background: #ffffff;
        box-shadow: 0 4px 16px rgba(37,99,235,0.08);
        animation: fadeInCard 1.2s cubic-bezier(.4,1.4,.6,1);
        border: 1px solid rgba(37,99,235,0.1);
    }
    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 1.05rem;
        background: transparent;
    }
    .modern-table thead tr {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 2;
        height: 60px;
    }
    .modern-table th, .modern-table td {
        padding: 1rem 0.8rem;
        text-align: left;
        vertical-align: middle;
    }
    .modern-table th {
        font-size: 1.1rem;
        letter-spacing: 0.02em;
    }
    .modern-table tbody tr {
        background: #ffffff;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(37,99,235,0.05);
        height: 60px;
    }
    .modern-table tbody tr:nth-child(even) {
        background: #f8faff;
    }
    .modern-table tbody tr:hover {
        background: #eef2ff;
        box-shadow: 0 2px 12px rgba(37,99,235,0.08);
        transform: translateY(-2px);
    }
    .modern-table input[type="checkbox"] {
        width: 1.2em;
        height: 1.2em;
        accent-color: #2563eb;
        cursor: pointer;
        border-radius: 4px;
        border: 2px solid #2563eb;
    }
    .modern-action-bar {
        display: flex;
        gap: 1.2rem;
        margin-top: 2rem;
        flex-wrap: wrap;
        align-items: stretch;
        animation: fadeInCard 1.3s cubic-bezier(.4,1.4,.6,1);
    }
    .modern-action-bar .btn {
        min-width: 200px;
        font-size: 1.08rem;
        font-weight: 600;
        border-radius: 0.7rem;
        box-shadow: 0 4px 12px rgba(37,99,235,0.1);
        transition: all 0.2s ease;
        padding: 0.9rem 1.5rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    .modern-action-bar .btn-danger {
        background: linear-gradient(90deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }
    .modern-action-bar .btn-danger:hover {
        background: linear-gradient(90deg, #b91c1c 0%, #dc2626 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(220,38,38,0.2);
    }
    .modern-action-bar .btn-primary {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
    }
    .modern-action-bar .btn-primary:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }
    /* We've moved the bottom bar styles into the action bar, so these styles are no longer needed */

    /* Remove underlines from all buttons and links */
    a, button, .btn {
        text-decoration: none !important;
    }
</style>

<div class="modern-card">
    <div class="modern-header">Attendance Dashboard</div>
    <form id="attendanceSearchForm" method="get" action="/dashboard" class="modern-search">
        <input type="text" name="search" id="attendanceSearchInput" placeholder="Search by ID or Name" value="{{ request.args.get('search', '') }}">
        <button type="submit"><i class="fas fa-search me-2"></i> Search</button>
    </form>
    <form method="get" action="/dashboard" class="modern-filter-bar">
        <div>
            <label for="department">Department</label>
            <select name="department" id="department">
                <option value="">All Departments</option>
                {% for dept in departments %}
                    <option value="{{ dept }}" {% if selected_dept == dept %}selected{% endif %}>{{ dept }}</option>
                {% endfor %}
            </select>
        </div>
        <div>
            <label for="date">Date</label>
            <input type="date" id="date" name="date" value="{{ selected_date }}">
        </div>
        <button type="submit" class="btn btn-primary"><i class="fas fa-filter me-2"></i> Apply Filters</button>
    </form>
    {% if attendance_records %}
        <div class="modern-table-container">
            <form id="attendanceTableForm" method="POST" action="/delete_attendance_records">
                <div style="max-height: 350px; overflow-y: auto;">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" onclick="toggleAllAttendance(this)"></th>
                                <th>Date</th>
                                <th>Time</th>
                                <th>Student ID</th>
                                <th>Name</th>
                                <th>Department</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in attendance_records %}
                            <tr>
                                <td><input type="checkbox" name="attendance_ids" value="{{ record.id }}"></td>
                                <td>{{ record.timestamp.strftime('%Y-%m-%d') }}</td>
                                <td>{{ record.timestamp.strftime('%H:%M:%S') }}</td>
                                <td>{{ record.student_code if record.student_code else (record.student.student_code if record.student else '[Student Deleted]') }}</td>
                                <td>{{ record.student_name if record.student_name else (record.student.name if record.student else '[Student Deleted]') }}</td>
                                <td>{{ record.student_department if record.student_department else (record.student.department if record.student else '[Student Deleted]') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Buttons moved below the table -->
                <div class="modern-action-bar" style="justify-content: center; margin-top: 2rem; padding: 1rem; background: #f8faff; border-radius: 0 0 1rem 1rem; border-top: 1px solid rgba(37,99,235,0.1);">
                    <a href="/export_attendance" class="btn btn-primary"><i class="fas fa-file-excel me-2"></i> Export to Excel</a>
                    <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete the selected attendance records? This action cannot be undone.')"><i class="fas fa-trash me-2"></i> Delete Selected Attendance</button>
                    <a href="{{ url_for('admin_panel') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Admin
                    </a>
                </div>
            </form>
        </div>
    {% else %}
        <div class="text-center py-8">
            <i class="fas fa-clipboard-list text-4xl text-gray-400"></i>
            <p class="mt-4 text-gray-600">No attendance records found for the selected criteria.</p>

            <!-- Add Back to Admin button for empty state -->
            <div style="margin-top: 2rem; display: flex; justify-content: center;">
                <a href="{{ url_for('admin_panel') }}" class="btn btn-secondary" style="min-width: 200px; font-size: 1.08rem; font-weight: 600; border-radius: 0.7rem; box-shadow: 0 4px 12px rgba(37,99,235,0.1); transition: all 0.2s ease; padding: 0.9rem 1.5rem; border: none; display: flex; align-items: center; justify-content: center; gap: 0.5rem; background: linear-gradient(90deg, #64748b 0%, #334155 100%); color: white;">
                    <i class="fas fa-arrow-left me-2"></i>
                    Back to Admin
                </a>
            </div>
        </div>
    {% endif %}
</div>

<script>
function toggleAllAttendance(source) {
    checkboxes = document.getElementsByName('attendance_ids');
    for(var i=0, n=checkboxes.length;i<n;i++) {
        checkboxes[i].checked = source.checked;
    }
}

document.getElementById('attendanceSearchForm').addEventListener('submit', function(e) {
    var val = document.getElementById('attendanceSearchInput').value.trim();
    if (!val) {
        e.preventDefault();
        window.location.href = '/dashboard';
    }
});

// Handle attendance deletion form submission
document.getElementById('attendanceTableForm').addEventListener('submit', function(e) {
    var checkboxes = document.querySelectorAll('input[name="attendance_ids"]:checked');
    if (checkboxes.length === 0) {
        e.preventDefault();
        alert('Please select at least one attendance record to delete.');
        return false;
    }
});
</script>
<script src="{{ url_for('static', filename='js/sleep-timer.js') }}"></script>
{% endblock %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Reset Admin Password</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f3f6fa;
            margin: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .reset-card {
            background: #fff;
            border-radius: 1.25rem;
            box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 2px 8px rgba(0,0,0,0.08);
            padding: 2.5rem 2rem 2rem 2rem;
            max-width: 400px;
            width: 90vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            animation: scaleIn 0.4s cubic-bezier(.4,1.4,.6,1);
        }
        @keyframes scaleIn {
            from { transform: scale(0.95); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: #1f2937;
            font-weight: 700;
        }
        .flash-message {
            min-width: 260px;
            max-width: 350px;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            font-weight: 500;
            box-shadow: 0 4px 16px rgba(37,99,235,0.10), 0 1.5px 6px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            background: #fff;
            color: #dc2626;
            opacity: 0;
            transform: translateY(-20px) scale(0.98);
            animation: flashIn 0.45s cubic-bezier(.4,1.4,.6,1) forwards;
            position: relative;
            margin-bottom: 1rem;
            border-left: 6px solid #dc2626;
        }
        @keyframes flashIn {
            from { opacity: 0; transform: translateY(-20px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .form-group {
            width: 100%;
            margin-bottom: 1.25rem;
        }
        label {
            display: block;
            font-weight: 500;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        input[type="password"], input[type="email"], input[type="text"] {
            width: 100%;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1.5px solid #e5e7eb;
            font-size: 1rem;
            background: #f9fafb;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-sizing: border-box;
        }
        input:focus {
            outline: none;
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37,99,235,0.10);
        }
        button, .btn {
            width: 100%;
            padding: 0.75rem;
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: #fff;
            border: none;
            border-radius: 0.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            margin-top: 0.5rem;
            cursor: pointer;
            transition: all 0.2s;
            box-shadow: 0 1px 3px rgba(37,99,235,0.08);
        }
        button:hover, .btn:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            box-shadow: 0 4px 12px rgba(37,99,235,0.12);
            transform: translateY(-2px);
        }
        .back-link {
            display: block;
            color: #2563eb;
            font-weight: 600;
            text-decoration: none;
            margin-top: 1.5rem;
            text-align: center;
            transition: color 0.2s;
        }
        .back-link:hover { color: #1d4ed8; text-decoration: underline; }
        .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6b7280;
            transition: color 0.2s;
            z-index: 2;
        }
        .toggle-password:hover {
            color: #2563eb;
        }
        @media (max-width: 500px) {
            .reset-card {
                max-width: 98vw;
                padding: 1.2rem 0.5rem;
            }
            h2 { font-size: 1.3rem; }
            button, .btn { font-size: 1rem; }
        }
    </style>
</head>
<body>
    <div class="reset-card">
        <h2>Reset Admin Password</h2>
        {% if message %}
            <div class="flash-message"><i class="fas fa-times-circle" style="color:#dc2626;"></i> <span>{{ message }}</span></div>
        {% endif %}

        {% if not show_otp_form and not show_password_form %}
        <form method="POST" action="/reset_password" style="width:100%;">
            <input type="hidden" name="step" value="email">
            <div class="form-group">
                <label for="email">Registered Email</label>
                <input type="email" id="email" name="email" placeholder="Enter your registered email" required>
            </div>
            <button type="submit" class="btn btn-primary">Send OTP</button>
        </form>
        {% elif show_otp_form %}
        <form method="POST" action="/reset_password" style="width:100%;">
            <input type="hidden" name="step" value="otp">
            <div class="form-group">
                <label for="otp">Enter OTP</label>
                <input type="text" id="otp" name="otp" maxlength="6" minlength="6" pattern="\d{6}" placeholder="6-digit OTP" required>
            </div>
            <button type="submit" class="btn btn-primary">Verify OTP</button>
        </form>
        {% elif show_password_form %}
        <form method="POST" action="/reset_password" style="width:100%;">
            <input type="hidden" name="step" value="password">
            <div class="form-group">
                <label for="new_password">New Password</label>
                <div style="position: relative;">
                    <input type="password" id="new_password" name="new_password" required>
                    <span class="toggle-password" onclick="togglePassword('new_password')">
                        <i class="fas fa-eye"></i>
                    </span>
                </div>
            </div>
            <div class="form-group">
                <label for="confirm_password">Confirm Password</label>
                <div style="position: relative;">
                    <input type="password" id="confirm_password" name="confirm_password" required>
                    <span class="toggle-password" onclick="togglePassword('confirm_password')">
                        <i class="fas fa-eye"></i>
                    </span>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">Reset Password</button>
        </form>
        {% endif %}
        <a href="{{ url_for('locked') if from_locked else url_for('login') }}" class="back-link">Back to Login</a>
    </div>
    <script>
    function togglePassword(inputId) {
        var input = document.getElementById(inputId);
        var icon = input.nextElementSibling.querySelector('i');
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }
    </script>
</body>
</html>
{% extends "base.html" %}

{% block content %}
<!-- Student Management Page -->
<style>
    .modern-card {
        background: white;
        border-radius: 1.5rem;
        box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 2px 8px rgba(0,0,0,0.08);
        padding: 2.5rem 2rem 2rem 2rem;
        max-width: 950px;
        margin: 2rem auto;
        animation: fadeInCard 0.7s cubic-bezier(.4,1.4,.6,1);
    }
    @keyframes fadeInCard {
        from { opacity: 0; transform: translateY(30px) scale(0.98); }
        to { opacity: 1; transform: translateY(0) scale(1); }
    }
    .modern-header {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border-radius: 1rem 1rem 0 0;
        padding: 1.5rem 2rem 1rem 2rem;
        margin: -2.5rem -2rem 2rem -2rem;
        box-shadow: 0 4px 16px rgba(37,99,235,0.08);
        font-size: 2.2rem;
        font-weight: 700;
        letter-spacing: -0.02em;
        text-shadow: 0 2px 8px rgba(37,99,235,0.10);
        animation: slideDown 0.7s cubic-bezier(.4,1.4,.6,1);
    }
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-30px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .modern-search {
        background: #f8faff;
        border-radius: 1rem;
        padding: 1.2rem 1.5rem;
        margin-bottom: 2rem;
        display: flex;
        gap: 1rem;
        align-items: center;
        box-shadow: 0 4px 16px rgba(37,99,235,0.06);
        animation: fadeInCard 1.1s cubic-bezier(.4,1.4,.6,1);
        border: 1px solid rgba(37,99,235,0.08);
    }
    .modern-search input[type="text"] {
        border-radius: 0.7rem;
        border: 1.5px solid #d1d5db;
        padding: 0.85rem 1.2rem;
        font-size: 1.1rem;
        transition: all 0.2s ease;
        flex: 1;
        background-color: white;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        color: #1f2937;
    }
    .modern-search input[type="text"]:focus {
        border-color: #2563eb;
        box-shadow: 0 0 0 3px rgba(37,99,235,0.10);
        outline: none;
    }
    .modern-search button {
        border-radius: 0.7rem;
        font-size: 1.1rem;
        padding: 0.85rem 1.8rem;
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        border: none;
        transition: all 0.2s ease;
        box-shadow: 0 4px 12px rgba(37,99,235,0.15);
        font-weight: 600;
        min-width: 120px;
        text-decoration: none;
    }
    .modern-search button:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }
    .modern-table-container {
        border-radius: 1rem;
        overflow: hidden;
        background: #ffffff;
        box-shadow: 0 4px 16px rgba(37,99,235,0.08);
        animation: fadeInCard 1.2s cubic-bezier(.4,1.4,.6,1);
        border: 1px solid rgba(37,99,235,0.1);
    }
    .modern-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 1.05rem;
        background: transparent;
    }
    .modern-table thead tr {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
        font-weight: 600;
        position: sticky;
        top: 0;
        z-index: 2;
        height: 60px;
    }
    .modern-table th, .modern-table td {
        padding: 1rem 0.8rem;
        text-align: left;
        vertical-align: middle;
    }
    .modern-table th {
        font-size: 1.1rem;
        letter-spacing: 0.02em;
    }
    .modern-table tbody tr {
        background: #ffffff;
        transition: all 0.2s ease;
        border-bottom: 1px solid rgba(37,99,235,0.05);
        height: 60px;
    }
    .modern-table tbody tr:nth-child(even) {
        background: #f8faff;
    }
    .modern-table tbody tr:hover {
        background: #eef2ff;
        box-shadow: 0 2px 12px rgba(37,99,235,0.08);
        transform: translateY(-2px);
    }
    .modern-table input[type="checkbox"] {
        width: 1.2em;
        height: 1.2em;
        accent-color: #2563eb;
        cursor: pointer;
        border-radius: 4px;
        border: 2px solid #2563eb;
    }
    .modern-action-bar {
        display: flex;
        gap: 1.2rem;
        margin-top: 2rem;
        flex-wrap: wrap;
        align-items: stretch;
        animation: fadeInCard 1.3s cubic-bezier(.4,1.4,.6,1);
    }
    .modern-action-bar .btn {
        min-width: 200px;
        font-size: 1.08rem;
        font-weight: 600;
        border-radius: 0.7rem;
        box-shadow: 0 4px 12px rgba(37,99,235,0.1);
        transition: all 0.2s ease;
        padding: 0.9rem 1.5rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    .modern-action-bar .btn-danger {
        background: linear-gradient(90deg, #dc2626 0%, #b91c1c 100%);
        color: white;
    }
    .modern-action-bar .btn-danger:hover {
        background: linear-gradient(90deg, #b91c1c 0%, #dc2626 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(220,38,38,0.2);
    }
    .modern-action-bar .btn-primary {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
    }
    .modern-action-bar .btn-primary:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }
    .modern-bottom-bar {
        display: flex;
        gap: 1.2rem;
        margin-top: 2.5rem;
        justify-content: flex-start;
        flex-wrap: wrap;
        animation: fadeInCard 1.4s cubic-bezier(.4,1.4,.6,1);
    }
    .modern-bottom-bar .btn {
        min-width: 200px;
        font-size: 1.08rem;
        font-weight: 600;
        border-radius: 0.7rem;
        box-shadow: 0 4px 12px rgba(37,99,235,0.1);
        transition: all 0.2s ease;
        padding: 0.9rem 1.5rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
    }
    .modern-bottom-bar .btn-secondary {
        background: linear-gradient(90deg, #64748b 0%, #334155 100%);
        color: white;
    }
    .modern-bottom-bar .btn-secondary:hover {
        background: linear-gradient(90deg, #334155 0%, #64748b 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(71,85,105,0.2);
    }
    .modern-bottom-bar .btn-primary {
        background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
        color: white;
    }
    .modern-bottom-bar .btn-primary:hover {
        background: linear-gradient(90deg, #1d4ed8 0%, #2563eb 100%);
        transform: translateY(-3px);
        box-shadow: 0 6px 16px rgba(37,99,235,0.2);
    }

    /* Remove underlines from all buttons and links */
    a, button, .btn {
        text-decoration: none !important;
    }
</style>

<div class="modern-card">
    <div class="modern-header">Student Management</div>
    <form id="studentSearchForm" method="get" action="/students" class="modern-search">
        <input type="text" name="search" id="studentSearchInput" placeholder="Search by ID or Name" value="{{ request.args.get('search', '') }}">
        <button type="submit"><i class="fas fa-search me-2"></i> Search</button>
    </form>
    <script>
    document.getElementById('studentSearchForm').addEventListener('submit', function(e) {
        var val = document.getElementById('studentSearchInput').value.trim();
        if (!val) {
            e.preventDefault();
            window.location.href = '/students';
        }
    });
    </script>
    {% if students %}
        <div class="modern-table-container">
            <form id="studentTableForm" method="POST">
                <div style="max-height: 350px; overflow-y: auto;">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th><input type="checkbox" onclick="toggleAll(this)"></th>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Department</th>
                                <th>Phone</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for student in students %}
                            <tr>
                                <td><input type="checkbox" name="student_ids" value="{{ student.id }}"></td>
                                <td>{{ student.student_code }}</td>
                                <td>{{ student.name }}</td>
                                <td>{{ student.department }}</td>
                                <td>{{ student.phone_number }}</td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-sm" style="background:linear-gradient(90deg,#2563eb 0%,#1d4ed8 100%);color:white;border-radius:0.5rem;padding:0.5rem 1rem;font-weight:500;box-shadow:0 2px 4px rgba(37,99,235,0.1);border:none;" onclick="openEditModal('{{ student.id }}', '{{ student.student_code }}', '{{ student.name }}', '{{ student.department }}', '{{ student.phone_number }}')">
                                        <i class="fas fa-edit me-1"></i> Edit
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="modern-action-bar">
                    <button type="submit" formaction="/delete_selected_student_details" class="btn btn-danger" onclick="return confirm('Delete only selected student details? Attendance history will be kept.');">
                        <i class="fas fa-user-minus me-2"></i>
                        Delete Details Only
                    </button>
                    <button type="submit" formaction="/delete_selected_students_and_attendance" class="btn btn-danger" onclick="return confirm('Delete selected student details and only their attendance history?');">
                        <i class="fas fa-user-times me-2"></i>
                        Delete Details + Their Attendance
                    </button>
                    <a href="/export_students" class="btn btn-primary" style="margin-left: auto;">
                        <i class="fas fa-file-excel me-2"></i> Export to Excel
                    </a>
                </div>
                            </form>
                        </div>
    {% else %}
        <div class="text-center py-8">
            <i class="fas fa-users text-4xl text-gray-400"></i>
            <p class="mt-4 text-gray-600">No students registered yet.</p>
        </div>
    {% endif %}
    <div class="modern-bottom-bar">
        <a href="{{ url_for('admin_panel') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Admin
        </a>
        <a href="{{ url_for('register') }}" class="btn btn-primary">
            <i class="fas fa-user-plus me-2"></i>
            Add New Student
        </a>
    </div>
</div>

<!-- Edit Student Modal -->
<div id="editModal" style="display:none; position:fixed; z-index:10000; left:0; top:0; width:100vw; height:100vh; background:rgba(0,0,0,0.4); align-items:center; justify-content:center; backdrop-filter: blur(4px);">
    <div style="background:white; border-radius:1.2rem; max-width:450px; width:95vw; margin:auto; padding:2.5rem 2rem; box-shadow:0 10px 40px rgba(37,99,235,0.2); position:relative; animation:fadeInCard 0.4s; border: 1px solid rgba(37,99,235,0.1);">
        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
            <div style="width: 40px; height: 40px; border-radius: 50%; background: rgba(37,99,235,0.1); display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                <i class="fas fa-user-edit" style="color: #2563eb; font-size: 1.2rem;"></i>
            </div>
            <h3 style="font-weight:700; color:#2563eb; margin: 0; font-size: 1.5rem;">Edit Student</h3>
        </div>
        <form id="editStudentForm" method="post">
            <input type="hidden" name="student_id" id="modal_student_id">
            <div class="mb-3">
                <label for="modal_student_code" class="form-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; font-size: 0.95rem;">Student ID</label>
                <input type="text" id="modal_student_code" name="student_code" class="form-control" required style="border-radius: 0.7rem; border: 1.5px solid #d1d5db; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.2s; box-shadow: 0 1px 2px rgba(0,0,0,0.05);">
            </div>
            <div class="mb-3">
                <label for="modal_name" class="form-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; font-size: 0.95rem;">Full Name</label>
                <input type="text" id="modal_name" name="name" class="form-control" required style="border-radius: 0.7rem; border: 1.5px solid #d1d5db; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.2s; box-shadow: 0 1px 2px rgba(0,0,0,0.05);">
            </div>
            <div class="mb-3">
                <label for="modal_department" class="form-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; font-size: 0.95rem;">Department</label>
                <input type="text" id="modal_department" name="department" class="form-control" required style="border-radius: 0.7rem; border: 1.5px solid #d1d5db; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.2s; box-shadow: 0 1px 2px rgba(0,0,0,0.05);">
            </div>
            <div class="mb-3">
                <label for="modal_phone_number" class="form-label" style="font-weight: 600; color: #374151; margin-bottom: 0.5rem; font-size: 0.95rem;">Phone Number</label>
                <input type="tel" id="modal_phone_number" name="phone_number" class="form-control" pattern="[0-9]{10}" required style="border-radius: 0.7rem; border: 1.5px solid #d1d5db; padding: 0.75rem 1rem; font-size: 1rem; transition: all 0.2s; box-shadow: 0 1px 2px rgba(0,0,0,0.05);">
            </div>
            <div class="d-flex gap-3 mt-4">
                <button type="submit" class="btn btn-primary" style="flex: 1; padding: 0.8rem; border-radius: 0.7rem; font-weight: 600; background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%); border: none; box-shadow: 0 4px 12px rgba(37,99,235,0.15);">
                    <i class="fas fa-save me-2"></i> Save Changes
                </button>
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()" style="flex: 1; padding: 0.8rem; border-radius: 0.7rem; font-weight: 600; background: linear-gradient(90deg, #64748b 0%, #475569 100%); border: none; box-shadow: 0 4px 12px rgba(71,85,105,0.15);">
                    <i class="fas fa-times me-2"></i> Cancel
                </button>
            </div>
        </form>
        <button onclick="closeEditModal()" style="position:absolute; top:1.2rem; right:1.2rem; background:rgba(0,0,0,0.05); border:none; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size:1rem; color:#64748b; cursor:pointer; transition: all 0.2s;">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<script>
function toggleAll(source) {
    checkboxes = document.getElementsByName('student_ids');
    for(var i=0, n=checkboxes.length;i<n;i++) {
        checkboxes[i].checked = source.checked;
    }
}
function openEditModal(id, code, name, dept, phone) {
    document.getElementById('editModal').style.display = 'flex';
    document.getElementById('modal_student_id').value = id;
    document.getElementById('modal_student_code').value = code;
    document.getElementById('modal_name').value = name;
    document.getElementById('modal_department').value = dept;
    document.getElementById('modal_phone_number').value = phone;
    document.getElementById('editStudentForm').action = '/edit_student/' + id;
}
function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
}
</script>
<script src="{{ url_for('static', filename='js/sleep-timer.js') }}"></script>
{% endblock %}
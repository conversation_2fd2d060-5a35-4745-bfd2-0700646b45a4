<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Face Recognition Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-ui.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/animations.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/components.css') }}" rel="stylesheet">
    <style>
        body {
            background-image:
                radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.1) 0%, transparent 40%),
                radial-gradient(circle at 80% 80%, rgba(5, 150, 105, 0.1) 0%, transparent 40%);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .login-card {
            max-width: 450px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1.5rem;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
            position: relative;
            padding: 3rem 2rem;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: var(--primary-gradient);
        }

        .login-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.5);
        }

        .login-icon i {
            font-size: 2.5rem;
            color: white;
        }

        .input-container {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .input-container i {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            transition: all var(--transition-normal);
        }

        .input-container input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 1rem;
            font-size: 1rem;
            transition: all var(--transition-normal);
            background: rgba(255, 255, 255, 0.8);
        }

        .input-container input:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
            background: white;
        }

        .input-container input:focus + i {
            color: var(--primary-color);
        }

        .toggle-password {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all var(--transition-normal);
        }

        .toggle-password:hover {
            background: rgba(0, 0, 0, 0.05);
            color: var(--primary-color);
        }

        .error-message {
            background-color: rgba(254, 226, 226, 0.9);
            color: var(--danger-color);
            padding: 1rem;
            border-radius: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.75rem;
            border-left: 4px solid var(--danger-color);
        }

        .error-message > div:first-child {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            width: 100%;
        }

        .countdown-timer {
            background-color: rgba(254, 202, 202, 0.5);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            margin-top: 0.5rem;
            width: 100%;
            text-align: center;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.8; }
            50% { opacity: 1; }
            100% { opacity: 0.8; }
        }

        .action-links {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        .forgot-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-normal);
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .forgot-link:hover {
            color: var(--primary-dark);
            transform: translateY(-2px);
        }

        .exit-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--success-gradient);
            color: white;
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            border-radius: 1rem;
            font-weight: 600;
            transition: all var(--transition-normal);
            box-shadow: var(--shadow-md);
        }

        .exit-link:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
            color: white;
        }
    </style>
</head>
<body>
    <div class="login-card animate-fade-in-up">
        <div class="login-icon animate-float" data-aos="zoom-in">
            <i class="fas fa-user-shield"></i>
        </div>

        <h2 class="text-center mb-4 text-gradient" data-aos="fade-up" data-aos-delay="100">Admin Login</h2>

        {% if error %}
        <div class="error-message animate-shake" data-aos="fade-up" data-aos-delay="150">
            <div>
                <i class="fas fa-exclamation-circle"></i>
                <span>{{ error }}</span>
            </div>
            {% if lockout_remaining > 0 %}
            <div id="countdown-timer" class="countdown-timer" data-seconds="{{ lockout_remaining }}">
                <span id="countdown-value">{{ lockout_remaining }}</span> seconds remaining
            </div>
            {% endif %}
        </div>
        {% endif %}

        <form method="POST" data-aos="fade-up" data-aos-delay="200" id="loginForm">
            <div class="input-container">
                <input type="password" name="password" id="password" placeholder="Enter password" required {% if lockout_remaining > 0 %}disabled{% endif %}>
                <i class="fas fa-lock"></i>
                <span class="toggle-password" id="togglePassword" tabindex="0" aria-label="Show password">
                    <i class="fas fa-eye"></i>
                </span>
            </div>

            <button type="submit" class="btn btn-primary w-100 py-3" {% if lockout_remaining > 0 %}disabled{% endif %}>
                <i class="fas fa-sign-in-alt me-2"></i> Login
            </button>
        </form>

        <div class="action-links">
            <a href="/reset_password" class="forgot-link" data-aos="fade-up" data-aos-delay="300">
                <i class="fas fa-key"></i> Forgot Password?
            </a>

            <a href="/" class="exit-link" data-aos="fade-up" data-aos-delay="400">
                <i class="fas fa-home"></i> Return to Home
            </a>
        </div>
    </div>

    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'cubic-bezier(0.25, 0.1, 0.25, 1.0)',
            once: true,
            offset: 50,
            delay: 100
        });

        // Show/hide password toggle
        document.addEventListener('DOMContentLoaded', function() {
            const passwordInput = document.getElementById('password');
            const togglePassword = document.getElementById('togglePassword');
            const eyeIcon = togglePassword ? togglePassword.querySelector('i') : null;
            const form = document.querySelector('form');

            if (passwordInput && togglePassword && eyeIcon) {
                function toggle() {
                    const isShown = passwordInput.type === 'text';
                    passwordInput.type = isShown ? 'password' : 'text';
                    eyeIcon.classList.toggle('fa-eye', isShown);
                    eyeIcon.classList.toggle('fa-eye-slash', !isShown);
                    togglePassword.setAttribute('aria-label', isShown ? 'Show password' : 'Hide password');

                    // Add animation
                    togglePassword.classList.add('animate-pulse');
                    setTimeout(() => {
                        togglePassword.classList.remove('animate-pulse');
                    }, 300);
                }

                togglePassword.addEventListener('click', toggle);
                togglePassword.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        toggle();
                    }
                });
            }

            // Add smooth page transitions
            document.body.classList.add('animate-fade-in');

            // Countdown timer
            const countdownTimer = document.getElementById('countdown-timer');
            if (countdownTimer) {
                const countdownValue = document.getElementById('countdown-value');
                let seconds = parseInt(countdownTimer.getAttribute('data-seconds'));

                // Disable the form if we're in lockout
                if (seconds > 0) {
                    const passwordInput = document.getElementById('password');
                    const submitButton = document.querySelector('button[type="submit"]');
                    if (passwordInput) passwordInput.disabled = true;
                    if (submitButton) submitButton.disabled = true;
                }

                const countdownInterval = setInterval(() => {
                    seconds--;
                    if (countdownValue) countdownValue.textContent = seconds;

                    if (seconds <= 0) {
                        clearInterval(countdownInterval);
                        // Enable the form again
                        const passwordInput = document.getElementById('password');
                        const submitButton = document.querySelector('button[type="submit"]');
                        if (passwordInput) passwordInput.disabled = false;
                        if (submitButton) submitButton.disabled = false;

                        // Reload the page to clear the lockout message
                        window.location.reload();
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>
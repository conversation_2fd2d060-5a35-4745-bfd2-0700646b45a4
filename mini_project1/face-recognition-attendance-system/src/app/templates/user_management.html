<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management - Face Recognition Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/modern-ui.css') }}" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
            color: #1e293b;
        }
        .user-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        .user-card {
            background: #fff;
            border-radius: 1.5rem;
            box-shadow: 0 10px 32px rgba(37,99,235,0.10);
            border: 1px solid #e5e7eb;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .user-title {
            font-size: 2rem;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 2rem;
            text-align: center;
        }
        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37,99,235,0.3);
        }
        .table-container {
            background: white;
            border-radius: 1rem;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        .table {
            margin-bottom: 0;
        }
        .table thead th {
            background: #f8fafc;
            border: none;
            font-weight: 600;
            color: #475569;
            padding: 1rem;
        }
        .table tbody td {
            padding: 1rem;
            border-color: #e2e8f0;
            vertical-align: middle;
        }
        .role-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .role-admin {
            background: #dbeafe;
            color: #1e40af;
        }
        .role-moderator {
            background: #fef3c7;
            color: #92400e;
        }
        .role-viewer {
            background: #f3f4f6;
            color: #374151;
        }
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 0.5rem;
            font-size: 0.75rem;
            font-weight: 600;
        }
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.875rem;
            border-radius: 0.5rem;
        }
        .back-btn {
            background: linear-gradient(135deg, #64748b 0%, #475569 100%);
            color: white;
            border: none;
            border-radius: 0.75rem;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(100,116,139,0.3);
            color: white;
            text-decoration: none;
        }
        .form-section {
            background: #f8fafc;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid #e2e8f0;
        }
        .form-control, .form-select {
            border-radius: 0.75rem;
            border: 1px solid #d1d5db;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }
        .activity-log {
            max-height: 300px;
            overflow-y: auto;
            background: #f8fafc;
            border-radius: 0.75rem;
            padding: 1rem;
        }
        .activity-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.875rem;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-time {
            color: #64748b;
            font-size: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="user-container">
        <div class="user-card">
            <h1 class="user-title">
                <i class="fas fa-user-shield me-3"></i>User Management
            </h1>

            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'success' if category == 'success' else 'danger' }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- Add New User Form -->
            <div class="form-section">
                <h5 class="mb-3"><i class="fas fa-user-plus me-2"></i>Add New User</h5>
                <form method="POST" action="{{ url_for('user_management') }}">
                    <input type="hidden" name="action" value="add_user">
                    <div class="row">
                        <div class="col-md-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-2">
                            <label for="role" class="form-label">Role</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="viewer">Viewer</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>Add User
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Users Table -->
            <div class="table-container">
                <table class="table">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Status</th>
                            <th>Last Login</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <strong>{{ user.username }}</strong>
                                {% if user.id == current_user.id %}
                                    <span class="badge bg-info ms-2">You</span>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                <span class="role-badge role-{{ user.role }}">
                                    {{ user.role.title() }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge status-{{ 'active' if user.is_active else 'inactive' }}">
                                    {{ 'Active' if user.is_active else 'Inactive' }}
                                </span>
                            </td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                    <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                            <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'N/A' }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                            onclick="editUser({{ user.id }}, '{{ user.username }}', '{{ user.email }}', '{{ user.role }}', {{ user.is_active|lower }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if user.id != current_user.id %}
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                        <input type="hidden" name="action" value="toggle_status">
                                        <input type="hidden" name="user_id" value="{{ user.id }}">
                                        <button type="submit" class="btn btn-outline-warning btn-sm">
                                            <i class="fas fa-{{ 'pause' if user.is_active else 'play' }}"></i>
                                        </button>
                                    </form>
                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Delete this user permanently?')">
                                        <input type="hidden" name="action" value="delete_user">
                                        <input type="hidden" name="user_id" value="{{ user.id }}">
                                        <button type="submit" class="btn btn-outline-danger btn-sm">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Recent Activity -->
            <div class="row">
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-history me-2"></i>Recent User Activity</h5>
                    <div class="activity-log">
                        {% for activity in recent_activities %}
                        <div class="activity-item">
                            <div><strong>{{ activity.username }}</strong> {{ activity.action }}</div>
                            <div class="activity-time">{{ activity.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 class="mb-3"><i class="fas fa-chart-pie me-2"></i>User Statistics</h5>
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-primary">{{ user_stats.total_users }}</h4>
                                <small>Total Users</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-success">{{ user_stats.active_users }}</h4>
                                <small>Active Users</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-info">{{ user_stats.admins }}</h4>
                                <small>Administrators</small>
                            </div>
                        </div>
                        <div class="col-6 mt-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h4 class="text-warning">{{ user_stats.online_now }}</h4>
                                <small>Online Now</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center mt-4">
                <button onclick="exportUsers()" class="btn btn-success me-3">
                    <i class="fas fa-file-excel me-2"></i>Export Users
                </button>
                <button onclick="sendBulkEmail()" class="btn btn-info me-3">
                    <i class="fas fa-envelope me-2"></i>Send Bulk Email
                </button>
                <a href="{{ url_for('admin_panel') }}" class="back-btn">
                    <i class="fas fa-arrow-left me-2"></i>Back to Admin Panel
                </a>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" id="editUserForm">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit_user">
                        <input type="hidden" name="user_id" id="editUserId">
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="editUsername" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="editRole" class="form-label">Role</label>
                            <select class="form-select" id="editRole" name="role" required>
                                <option value="viewer">Viewer</option>
                                <option value="moderator">Moderator</option>
                                <option value="admin">Admin</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editPassword" class="form-label">New Password (leave blank to keep current)</label>
                            <input type="password" class="form-control" id="editPassword" name="password">
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                            <label class="form-check-label" for="editIsActive">
                                Active User
                            </label>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editUser(id, username, email, role, isActive) {
            document.getElementById('editUserId').value = id;
            document.getElementById('editUsername').value = username;
            document.getElementById('editEmail').value = email;
            document.getElementById('editRole').value = role;
            document.getElementById('editIsActive').checked = isActive;

            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        }

        function exportUsers() {
            window.open('/export_users', '_blank');
        }

        function sendBulkEmail() {
            // Create a modal for bulk email
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Send Bulk Email</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <form method="POST" action="/send_bulk_email">
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="emailSubject" class="form-label">Subject</label>
                                    <input type="text" class="form-control" id="emailSubject" name="subject" required>
                                </div>
                                <div class="mb-3">
                                    <label for="emailMessage" class="form-label">Message</label>
                                    <textarea class="form-control" id="emailMessage" name="message" rows="4" required></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Recipients</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="recipients" value="all" id="allUsers" checked>
                                        <label class="form-check-label" for="allUsers">All Users</label>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" class="btn btn-primary">Send Email</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            new bootstrap.Modal(modal).show();

            // Remove modal after it's hidden
            modal.addEventListener('hidden.bs.modal', function() {
                document.body.removeChild(modal);
            });
        }
    </script>
</body>
</html>

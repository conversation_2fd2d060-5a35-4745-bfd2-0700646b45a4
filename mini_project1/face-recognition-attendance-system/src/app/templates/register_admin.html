<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Register Admin</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .navbar {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .navbar-brand {
            color: white !important;
            font-weight: 600;
        }
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            transition: color 0.2s;
        }
        .nav-link:hover {
            color: white !important;
        }
        .register-card {
            max-width: 400px;
            margin: 60px auto 0 auto;
            border: none;
            box-shadow: 0 4px 12px rgba(37,99,235,0.08);
            border-radius: 1rem;
            background: #fff;
            padding: 2.5rem 2rem 2rem 2rem;
        }
        h2 {
            text-align: center;
            font-size: 2rem;
            margin-bottom: 1.5rem;
            color: #1f2937;
            font-weight: 700;
        }
        .flash-message {
            color: #dc2626;
            text-align: center;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: #fee2e2;
            border-radius: 8px;
            width: 100%;
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .form-control {
            border-radius: 0.5rem;
            border: 1px solid #e5e7eb;
            padding: 0.75rem 1rem;
        }
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37,99,235,0.1);
        }
        .form-label {
            font-weight: 500;
            color: #374151;
        }
        .btn-primary {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 0.75rem 0;
            width: 100%;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(37,99,235,0.2);
        }
        .back-link {
            display:inline-block;
            color: #2563eb;
            font-weight: 600;
            text-decoration: none;
            margin-top: 1.5rem;
            text-align: center;
            width: 100%;
        }
        .back-link:hover { color: #1d4ed8; }
        @media (max-width: 500px) {
            .register-card {
                max-width: 98vw;
                padding: 1.2rem 0.5rem;
            }
            h2 { font-size: 1.3rem; }
            .btn-primary { font-size: 1rem; }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-camera-retro me-2"></i>
                Face Recognition Attendance
            </a>
        </div>
    </nav>
    <div class="container">
        <div class="register-card mt-5">
            <h2>Register Admin</h2>
            {% if message %}
                <div class="flash-message">{{ message }}</div>
            {% endif %}

            {% if not show_otp_form and not show_password_form %}
            <form method="POST" action="/register_admin">
                <input type="hidden" name="step" value="register">
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" id="username" name="username" class="form-control" placeholder="Enter username" required>
                </div>
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" id="email" name="email" class="form-control" placeholder="Enter email" required>
                </div>
                <button type="submit" class="btn btn-primary">Send OTP</button>
            </form>
            {% elif show_otp_form %}
            <form method="POST" action="/register_admin">
                <input type="hidden" name="step" value="otp">
                <div class="mb-3">
                    <label for="otp" class="form-label">Enter OTP</label>
                    <input type="text" id="otp" name="otp" maxlength="6" minlength="6" pattern="\d{6}" class="form-control" placeholder="6-digit OTP" required>
                </div>
                <button type="submit" class="btn btn-primary">Verify OTP</button>
            </form>
            {% elif show_password_form %}
            <form method="POST" action="/register_admin">
                <input type="hidden" name="step" value="password">
                <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <input type="password" id="new_password" name="new_password" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label for="confirm_password" class="form-label">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                </div>
                <button type="submit" class="btn btn-primary">Register Admin</button>
            </form>
            {% endif %}
            <a href="/login" class="back-link">Back to Login</a>
        </div>
    </div>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
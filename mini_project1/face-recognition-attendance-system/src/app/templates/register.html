<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Registration - Face Recognition Attendance</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #e0e7ff 0%, #f3f4f6 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            padding: 2rem 0;
        }
        .card {
            border: none;
            box-shadow: 0 8px 32px rgba(37,99,235,0.10), 0 2px 8px rgba(0,0,0,0.08);
            border-radius: 1.5rem;
            animation: fadeInCard 0.7s cubic-bezier(.4,1.4,.6,1);
        }
        @keyframes fadeInCard {
            from { opacity: 0; transform: translateY(30px) scale(0.98); }
            to { opacity: 1; transform: translateY(0) scale(1); }
        }
        .card-header {
            background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            border-radius: 1.5rem 1.5rem 0 0;
            padding: 1.5rem 2rem;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
        }
        .card-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 0.7rem;
            border: 1.5px solid #e5e7eb;
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
            transition: all 0.2s;
        }
        .form-control:focus {
            border-color: #2563eb;
            box-shadow: 0 0 0 3px rgba(37,99,235,0.1);
        }
        .form-label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        .btn {
            border-radius: 0.7rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(90deg, #2563eb 0%, #1d4ed8 100%);
            border: none;
        }
        .btn-secondary {
            background: linear-gradient(90deg, #64748b 0%, #334155 100%);
            border: none;
        }
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        .flash-message {
            margin-bottom: 10px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            animation: fadeIn 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Flash Messages -->
    <div class="flash-messages">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="flash-message alert alert-{{ category }}">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif category == 'danger' %}
                            <i class="fas fa-times-circle me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% elif category == 'info' %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-user-plus me-2"></i> Student Registration
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('register') }}">
                            <div class="mb-4">
                                <label for="student_id" class="form-label">Student ID</label>
                                <input type="text" class="form-control" id="student_id" name="student_id" required>
                            </div>
                            <div class="mb-4">
                                <label for="name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-4">
                                <label for="department" class="form-label">Department</label>
                                <input type="text" class="form-control" id="department" name="department" required>
                            </div>
                            <div class="mb-4">
                                <label for="phone_number" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone_number" name="phone_number" pattern="[0-9]{10}" required>
                                <div class="form-text">Enter a 10-digit phone number</div>
                            </div>
                            <div class="d-flex gap-3">
                                <button type="submit" class="btn btn-primary flex-grow-1">
                                    <i class="fas fa-arrow-right me-2"></i> Continue to Face Capture
                                </button>
                                <a href="{{ url_for('index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/sleep-timer.js') }}"></script>
</body>
</html> 
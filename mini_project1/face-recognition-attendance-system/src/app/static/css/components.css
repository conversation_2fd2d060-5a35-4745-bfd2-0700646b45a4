/* UI Components for Face Recognition Attendance System
   Created by Augment Agent - Professional UI Upgrade
*/

/* Navbar Styles */
.navbar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: var(--shadow-md);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
}

.navbar-brand {
    color: white !important;
    font-weight: 800;
    font-size: 1.5rem;
    letter-spacing: -0.5px;
    transition: all var(--transition-normal);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    padding: 0.5rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    font-size: 1.4rem;
    color: rgba(255, 255, 255, 0.95);
}

.navbar-brand span {
    background: linear-gradient(to right, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 900;
}

.navbar-brand:hover {
    transform: translateY(-1px);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

.nav-link {
    color: rgba(255,255,255,0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all var(--transition-normal);
}

.nav-link:hover {
    color: white !important;
    background: rgba(255,255,255,0.1);
    transform: translateY(-1px);
}

.nav-actions-fixed {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 2000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 110%;
    right: 0;
    min-width: 180px;
    background: rgba(255,255,255,0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all var(--transition-normal);
    z-index: 1000;
}

.dropdown:hover .dropdown-menu,
.dropdown:focus-within .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    color: var(--text-primary);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
    border-bottom: 1px solid var(--border-color);
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(0,0,0,0.05);
}

/* Alert/Flash Messages */
.flash-messages {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 999;
    max-width: 320px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 0.75rem;
    pointer-events: none;
}

.flash-message {
    padding: 0.75rem 1rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-md);
    animation: slideInFromRight 0.5s ease-out;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.95);
    pointer-events: auto;
    margin-bottom: 0.25rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.9rem;
}

.flash-message i {
    font-size: 1rem;
}

.flash-message:hover {
    transform: translateX(-3px);
    box-shadow: var(--shadow-lg);
}

.flash-message.alert-success {
    background-color: rgba(220, 252, 231, 0.9);
    color: var(--success-dark);
    border-left: 4px solid var(--success-color);
}

.flash-message.alert-danger {
    background-color: rgba(254, 226, 226, 0.9);
    color: var(--danger-dark);
    border-left: 4px solid var(--danger-color);
}

.flash-message.alert-warning {
    background-color: rgba(254, 243, 199, 0.9);
    color: var(--warning-dark);
    border-left: 4px solid var(--warning-color);
}

.flash-message.alert-info {
    background-color: rgba(224, 242, 254, 0.9);
    color: var(--info-dark);
    border-left: 4px solid var(--info-color);
}

/* Video Container */
.video-container {
    position: relative;
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    border-radius: 1.5rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    background: var(--card-background);
}

.video-container:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.video-container video,
.video-container img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.video-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.loading-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-success {
    background-color: var(--success-color);
    box-shadow: 0 0 0 4px rgba(5, 150, 105, 0.2);
}

.status-danger {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.2);
}

.status-warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 4px rgba(217, 119, 6, 0.2);
}

.status-info {
    background-color: var(--info-color);
    box-shadow: 0 0 0 4px rgba(2, 132, 199, 0.2);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Tables */
.table-container {
    overflow-x: auto;
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    background: var(--card-background);
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background-color: rgba(37, 99, 235, 0.05);
    color: var(--text-primary);
    font-weight: 600;
    text-align: left;
    padding: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.table tr:last-child td {
    border-bottom: none;
}

.table tr:hover td {
    background-color: rgba(37, 99, 235, 0.05);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 9999px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.badge-success {
    background-color: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.badge-danger {
    background-color: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.badge-warning {
    background-color: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.badge-info {
    background-color: rgba(2, 132, 199, 0.1);
    color: var(--info-color);
}

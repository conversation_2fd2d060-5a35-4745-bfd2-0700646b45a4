/* Modern UI CSS for Face Recognition Attendance System
   Created by Augment Agent - Professional UI Upgrade
*/

:root {
    /* Primary Colors */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);

    /* Secondary Colors */
    --success-color: #059669;
    --success-light: #10b981;
    --success-dark: #047857;
    --success-gradient: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);

    /* Alert Colors */
    --danger-color: #dc2626;
    --danger-light: #ef4444;
    --danger-dark: #b91c1c;
    --danger-gradient: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);

    --warning-color: #d97706;
    --warning-light: #f59e0b;
    --warning-dark: #b45309;

    --info-color: #0284c7;
    --info-light: #0ea5e9;
    --info-dark: #0369a1;

    /* Neutral Colors */
    --background-color: #f8fafc;
    --card-background: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Animation Timing */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--background-color);
    background-image: radial-gradient(circle at 80% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 40%),
                      radial-gradient(circle at 20% 80%, rgba(5, 150, 105, 0.05) 0%, transparent 40%);
    background-attachment: fixed;
    color: var(--text-primary);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
    transition: background-color var(--transition-normal);
}

/* Remove underlines from all links */
a {
    text-decoration: none;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

h1 {
    font-size: 2.5rem;
    letter-spacing: -0.025em;
}

h2 {
    font-size: 2rem;
    letter-spacing: -0.025em;
}

h3 {
    font-size: 1.5rem;
    letter-spacing: -0.025em;
}

p {
    line-height: 1.6;
    margin-bottom: 1rem;
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.text-success-gradient {
    background: var(--success-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

/* Layout Components */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: -0.5rem;
}

.col {
    flex: 1 0 0%;
    padding: 0.5rem;
}

/* Card Component */
.card {
    background: var(--card-background);
    border-radius: 1rem;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    overflow: hidden;
    border: none;
    margin-bottom: 1.5rem;
    position: relative;
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background: var(--card-background);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--card-background);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
    letter-spacing: 0.025em;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-success {
    background: var(--success-gradient);
    color: white;
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
}

.btn-outline {
    background: transparent;
    border: 2px solid currentColor;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: rgba(37, 99, 235, 0.1);
}

/* Form Elements */
.form-control {
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    transition: all var(--transition-normal);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.1);
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fade-in {
    animation: fadeIn 0.5s ease-out;
}

.animate-slide-up {
    animation: slideInUp 0.5s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.justify-content-center { justify-content: center; }
.align-items-center { align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

/* Navbar Styles */
.navbar {
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all var(--transition-normal);
    margin: 0 0.25rem;
}

.nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.nav-link.active {
    color: white !important;
    background: rgba(37, 99, 235, 0.2);
}

.dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(37, 99, 235, 0.2);
    border-radius: 0.75rem;
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
    margin-top: 0.5rem;
    animation: fadeIn 0.3s ease-out;
}

.dropdown-item {
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all var(--transition-normal);
    font-weight: 500;
}

.dropdown-item:hover {
    background: rgba(37, 99, 235, 0.1);
    transform: translateX(5px);
}

.dropdown-toggle::after {
    margin-left: 0.5rem;
    vertical-align: middle;
}

/* Responsive utilities */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }

    .container { padding: 0.75rem; }
    .card { margin-bottom: 1rem; }
    .card-header, .card-body, .card-footer { padding: 1rem; }

    .btn { padding: 0.5rem 1rem; }

    .navbar-collapse {
        background: rgba(0, 0, 0, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 1rem;
        padding: 1rem;
        margin-top: 0.5rem;
    }

    .nav-link {
        padding: 0.75rem 1rem;
        margin: 0.25rem 0;
    }
}

#!/usr/bin/env python3
"""
Model Warmup Utility

This module provides utilities to preload and warm up face recognition models
to avoid delays during the first detection request.
"""

import os
import sys
import time
import logging
import numpy as np
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def warmup_insightface_model(verbose: bool = True) -> bool:
    """
    Warm up the InsightFace model by running a test inference.
    
    Args:
        verbose: Whether to print progress messages
        
    Returns:
        bool: True if warmup was successful, False otherwise
    """
    try:
        if verbose:
            print("🧠 Warming up InsightFace model...")
        
        # Import face utilities
        from src.services.face_utils import initialize_insightface, app as face_app
        
        # Check if model is already initialized
        if face_app is None:
            if verbose:
                print("   ⚠️  InsightFace not initialized, initializing now...")
            face_app = initialize_insightface()
        
        if face_app is None:
            if verbose:
                print("   ❌ Failed to initialize InsightFace model")
            return False
        
        if verbose:
            print("   ✅ InsightFace model loaded successfully")
        
        # Create test images of different sizes for comprehensive warmup
        test_sizes = [(640, 640), (480, 480), (320, 320)]
        
        for i, (width, height) in enumerate(test_sizes):
            if verbose:
                print(f"   🔥 Warming up with test image {i+1}/{len(test_sizes)} ({width}x{height})...")
            
            # Create a test image with some variation
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            
            # Add some simple patterns to make it more realistic
            test_image[height//4:3*height//4, width//4:3*width//4] = 128
            
            start_time = time.time()
            try:
                result = face_app.get(test_image)
                inference_time = time.time() - start_time
                
                if verbose:
                    print(f"   ✅ Test {i+1} completed in {inference_time:.3f}s - detected {len(result)} faces")
                    
            except Exception as e:
                if verbose:
                    print(f"   ⚠️  Test {i+1} failed (non-critical): {e}")
                logger.warning(f"Warmup test {i+1} failed: {e}")
        
        if verbose:
            print("✅ InsightFace model warmup completed successfully")
        
        return True
        
    except Exception as e:
        if verbose:
            print(f"❌ Error during model warmup: {e}")
        logger.error(f"Model warmup failed: {e}")
        return False

def warmup_all_models(verbose: bool = True) -> bool:
    """
    Warm up all face recognition models used by the system.
    
    Args:
        verbose: Whether to print progress messages
        
    Returns:
        bool: True if all warmups were successful, False otherwise
    """
    try:
        if verbose:
            print("🚀 Starting comprehensive model warmup...")
        
        start_time = time.time()
        
        # Warmup InsightFace model
        insightface_success = warmup_insightface_model(verbose)
        
        # Test face embedding extraction
        if verbose:
            print("🧪 Testing face embedding extraction...")
        
        try:
            from src.services.face_utils import extract_face_embedding
            
            # Create a test face-like image
            test_face = np.random.randint(50, 200, (100, 100, 3), dtype=np.uint8)
            
            # Add some face-like features (simple rectangles for eyes, nose, mouth)
            test_face[20:30, 25:35] = 0  # Left eye
            test_face[20:30, 65:75] = 0  # Right eye
            test_face[45:55, 45:55] = 0  # Nose
            test_face[70:75, 35:65] = 0  # Mouth
            
            embedding_start = time.time()
            embedding = extract_face_embedding(test_face)
            embedding_time = time.time() - embedding_start
            
            if embedding is not None:
                if verbose:
                    print(f"   ✅ Face embedding extraction test completed in {embedding_time:.3f}s")
            else:
                if verbose:
                    print("   ⚠️  Face embedding extraction returned None (expected for test image)")
                    
        except Exception as e:
            if verbose:
                print(f"   ⚠️  Face embedding test failed (non-critical): {e}")
            logger.warning(f"Face embedding test failed: {e}")
        
        total_time = time.time() - start_time
        
        if verbose:
            print(f"✅ Model warmup completed in {total_time:.2f} seconds")
            print("🎯 System is ready for face recognition!")
        
        return insightface_success
        
    except Exception as e:
        if verbose:
            print(f"❌ Error during comprehensive model warmup: {e}")
        logger.error(f"Comprehensive model warmup failed: {e}")
        return False

def check_model_status(verbose: bool = True) -> dict:
    """
    Check the status of all face recognition models.
    
    Args:
        verbose: Whether to print status messages
        
    Returns:
        dict: Status information for each model
    """
    status = {
        'insightface': False,
        'face_utils': False,
        'overall': False
    }
    
    try:
        if verbose:
            print("🔍 Checking model status...")
        
        # Check InsightFace
        try:
            from src.services.face_utils import app as face_app
            if face_app is not None:
                status['insightface'] = True
                if verbose:
                    print("   ✅ InsightFace model: Loaded")
            else:
                if verbose:
                    print("   ❌ InsightFace model: Not loaded")
        except Exception as e:
            if verbose:
                print(f"   ❌ InsightFace model: Error - {e}")
        
        # Check face_utils module
        try:
            from src.services import face_utils
            status['face_utils'] = True
            if verbose:
                print("   ✅ Face utilities module: Available")
        except Exception as e:
            if verbose:
                print(f"   ❌ Face utilities module: Error - {e}")
        
        # Overall status
        status['overall'] = status['insightface'] and status['face_utils']
        
        if verbose:
            if status['overall']:
                print("✅ All models are ready")
            else:
                print("❌ Some models are not ready")
        
        return status
        
    except Exception as e:
        if verbose:
            print(f"❌ Error checking model status: {e}")
        logger.error(f"Model status check failed: {e}")
        return status

def main():
    """Main function for running model warmup as a standalone script."""
    print("🎯 Face Recognition Model Warmup Utility")
    print("=" * 50)
    
    # Check current status
    status = check_model_status(verbose=True)
    print()
    
    # Perform warmup
    success = warmup_all_models(verbose=True)
    print()
    
    # Final status check
    print("Final status check:")
    final_status = check_model_status(verbose=True)
    
    if success and final_status['overall']:
        print("\n🎉 Model warmup completed successfully!")
        return True
    else:
        print("\n⚠️  Model warmup completed with some issues.")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Model warmup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error during model warmup: {e}")
        sys.exit(1)

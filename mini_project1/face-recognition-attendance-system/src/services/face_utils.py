"""
Face recognition utilities using InsightFace.
This module provides functions for face detection and recognition.
"""

import numpy as np
import json
import logging
import os
import threading
import gc
import cv2
from functools import lru_cache
from typing import List, Tuple, Optional
import queue

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Face recognition configuration - Real-world production settings
FACE_RECOGNITION_THRESHOLD = float(os.environ.get('FACE_RECOGNITION_THRESHOLD', '0.5'))  # Stricter threshold
FACE_DETECTION_SIZE = int(os.environ.get('FACE_DETECTION_SIZE', '640'))
FACE_CACHE_SIZE = int(os.environ.get('FACE_CACHE_SIZE', '1000'))  # Cache size for face embeddings
FRAME_SKIP = int(os.environ.get('FRAME_SKIP', '2'))  # Process 1 frame every N frames (reduced for smoother detection)
USE_GPU = os.environ.get('USE_GPU', 'True').lower() in ('true', '1', 't')  # Use GPU if available
DETECTION_FREQUENCY = int(os.environ.get('DETECTION_FREQUENCY', '10'))  # Detect faces every N frames
VERIFICATION_THRESHOLD = float(os.environ.get('VERIFICATION_THRESHOLD', '0.6'))  # Stricter verification threshold

# Liveness Detection Configuration - Balanced security and usability
LIVENESS_ENABLED = os.environ.get('LIVENESS_ENABLED', 'True').lower() in ('true', '1', 't')
LIVENESS_THRESHOLD = float(os.environ.get('LIVENESS_THRESHOLD', '0.65'))  # Balanced liveness confidence threshold
MIN_FACE_SIZE = int(os.environ.get('MIN_FACE_SIZE', '80'))  # Minimum face size in pixels
MAX_FACE_SIZE = int(os.environ.get('MAX_FACE_SIZE', '400'))  # Maximum face size in pixels
FACE_QUALITY_THRESHOLD = float(os.environ.get('FACE_QUALITY_THRESHOLD', '0.3'))  # Balanced face quality threshold
MOTION_DETECTION_FRAMES = int(os.environ.get('MOTION_DETECTION_FRAMES', '5'))  # Frames to detect motion
BRIGHTNESS_VARIANCE_THRESHOLD = float(os.environ.get('BRIGHTNESS_VARIANCE_THRESHOLD', '15.0'))  # Detect screen glare
TEXTURE_ANALYSIS_THRESHOLD = float(os.environ.get('TEXTURE_ANALYSIS_THRESHOLD', '0.25'))  # Balanced texture analysis threshold

# Initialize InsightFace face detector
app = None

def initialize_insightface():
    """Initialize InsightFace face detector and recognition model."""
    global app
    if app is not None:
        logger.info("InsightFace already initialized")
        return app

    try:
        import insightface
        from insightface.app import FaceAnalysis
        import platform
        import onnxruntime as ort

        # Check if USE_GPU is enabled
        USE_GPU = os.environ.get('USE_GPU', 'True').lower() in ('true', '1', 't')

        # Get available providers
        available_providers = ort.get_available_providers()
        logger.info(f"Available ONNX Runtime providers: {available_providers}")

        # Determine the appropriate provider based on platform and USE_GPU setting
        if USE_GPU:
            if platform.system() == 'Darwin' and platform.processor() == 'arm':
                # Mac M1/M2 (Apple Silicon)
                if 'CoreMLExecutionProvider' in available_providers:
                    logger.info("Using CoreML provider for GPU acceleration on Mac M1/M2")
                    providers = ['CoreMLExecutionProvider', 'CPUExecutionProvider']
                else:
                    logger.info("CoreML provider not available, falling back to CPU")
                    providers = ['CPUExecutionProvider']
            else:
                # Other platforms with GPU
                if 'CUDAExecutionProvider' in available_providers:
                    logger.info("Using CUDA provider for GPU acceleration")
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                else:
                    logger.info("CUDA provider not available, falling back to CPU")
                    providers = ['CPUExecutionProvider']
        else:
            # CPU only
            logger.info("Using CPU only as specified by USE_GPU setting")
            providers = ['CPUExecutionProvider']

        # Initialize FaceAnalysis with the appropriate providers
        app = FaceAnalysis(providers=providers)
        app.prepare(ctx_id=0, det_size=(640, 640))

        # Log which provider is being used
        logger.info(f"Initialized InsightFace face detector and recognition with providers: {providers}")

        # Test the initialization with a simple image
        test_img = np.zeros((100, 100, 3), dtype=np.uint8)
        try:
            test_result = app.get(test_img)
            logger.info(f"InsightFace test: {test_result}")
        except Exception as test_e:
            logger.warning(f"InsightFace test failed (non-critical): {test_e}")

        return app
    except Exception as e:
        logger.error(f"Failed to initialize InsightFace face detector: {e}")
        return None

# Initialize InsightFace on module load
app = initialize_insightface()



# Global variables
processing_queue = queue.Queue(maxsize=10)
processing_thread = None
processing_active = False
frame_counter = 0

@lru_cache(maxsize=FACE_CACHE_SIZE)
def _get_cached_embedding(embedding_key):
    """Cache for face embeddings to avoid repeated processing.

    Args:
        embedding_key: Can be either a JSON string or a tuple of (student_id, timestamp)
                      for retrieving from database

    Returns:
        numpy.ndarray: The face embedding as a numpy array
    """
    try:
        if isinstance(embedding_key, str):
            # Legacy JSON format
            return np.array(json.loads(embedding_key))
        elif isinstance(embedding_key, tuple):
            # Database lookup format (student_id, timestamp)
            from src.models.db import db, Student
            student_id = embedding_key[0]
            student = db.session.query(Student).get(student_id)
            if student and student.face_embedding is not None:
                return np.array(student.face_embedding)
        return None
    except Exception as e:
        logger.error(f"Error retrieving cached embedding: {e}")
        return None

def preprocess_image(image: np.ndarray) -> np.ndarray:
    """Preprocess image for better face detection.

    Args:
        image: Input image as numpy array

    Returns:
        Preprocessed image
    """
    # Check if image needs resizing
    if image.shape[0] > FACE_DETECTION_SIZE or image.shape[1] > FACE_DETECTION_SIZE:
        scale = FACE_DETECTION_SIZE / max(image.shape[0], image.shape[1])
        new_size = (int(image.shape[1] * scale), int(image.shape[0] * scale))
        image = cv2.resize(image, new_size)

    # Apply histogram equalization to improve contrast
    if len(image.shape) == 3 and image.shape[2] == 3:
        # Convert to YUV and equalize the Y channel
        yuv = cv2.cvtColor(image, cv2.COLOR_BGR2YUV)
        yuv[:,:,0] = cv2.equalizeHist(yuv[:,:,0])
        image = cv2.cvtColor(yuv, cv2.COLOR_YUV2BGR)

    return image

def detect_faces(image: np.ndarray) -> List[Tuple[int, int, int, int]]:
    """Detect faces in an image using InsightFace.

    Args:
        image: Input image as numpy array

    Returns:
        List of face bounding boxes (x, y, w, h)
    """
    if image is None:
        logger.error("Received None image for face detection")
        return []

    try:
        # Use InsightFace for detection
        if app is not None:
            try:
                # Use InsightFace for detection
                faces = app.get(image)
                if faces and len(faces) > 0:
                    # Convert InsightFace format to (x, y, w, h)
                    result = []
                    for face in faces:
                        bbox = face.bbox.astype(int)
                        x1, y1, x2, y2 = bbox

                        # Add a small margin around the face for better visibility
                        margin_x = int((x2 - x1) * 0.1)  # 10% margin
                        margin_y = int((y2 - y1) * 0.1)  # 10% margin

                        # Ensure we don't go out of bounds
                        x1 = max(0, x1 - margin_x)
                        y1 = max(0, y1 - margin_y)
                        x2 = min(image.shape[1], x2 + margin_x)
                        y2 = min(image.shape[0], y2 + margin_y)

                        w = x2 - x1
                        h = y2 - y1
                        result.append((x1, y1, w, h))

                    logger.info(f"InsightFace detected {len(result)} faces")
                    print(f"InsightFace detected {len(result)} faces")
                    return result
            except Exception as e:
                logger.error(f"Error using InsightFace: {e}")
                print(f"Error using InsightFace: {e}")
                return []
        else:
            logger.warning("InsightFace not initialized")
            return []

        logger.warning("No faces detected")
        return []
    except Exception as e:
        logger.error(f"Error in detect_faces: {e}")
        print(f"Error in detect_faces: {e}")
        return []

def extract_face_embedding(image: np.ndarray) -> Optional[List[float]]:
    """Extract embedding from a face image using InsightFace.

    Args:
        image: Input image as numpy array

    Returns:
        List of floats representing the face embedding, or None if no face detected
    """
    try:
        if image is None:
            logger.warning("Received None image for face embedding extraction")
            return None

        # Debug: Log image properties
        logger.info(f"extract_face_embedding: image shape={image.shape}, dtype={image.dtype}, sample_pixel={image[0,0] if image.size > 0 else 'empty'}")

        # Preprocess image for better detection
        processed_image = preprocess_image(image)

        # Debug: Log processed image properties
        logger.info(f"extract_face_embedding: processed_image shape={processed_image.shape}, dtype={processed_image.dtype}, sample_pixel={processed_image[0,0] if processed_image.size > 0 else 'empty'}")

        # Make sure InsightFace is initialized
        global app
        if app is None:
            logger.warning("InsightFace not initialized in extract_face_embedding, attempting to initialize...")
            app = initialize_insightface()
            if app is None:
                logger.error("Failed to initialize InsightFace in extract_face_embedding")
                return None

        # Use InsightFace for embedding extraction
        if app is not None:
            try:
                # Use InsightFace for detection and embedding
                faces = app.get(processed_image)

                # Log detection results
                if faces:
                    logger.info(f"InsightFace detected {len(faces)} faces")
                    for i, face in enumerate(faces):
                        bbox = face.bbox
                        logger.info(f"Face {i+1}: bbox={bbox}, has_embedding={hasattr(face, 'embedding')}")
                else:
                    logger.warning("No faces detected by InsightFace")
                    return None

                if faces and len(faces) > 0:
                    # Get the largest face
                    largest_face = max(faces, key=lambda face: (face.bbox[2] - face.bbox[0]) * (face.bbox[3] - face.bbox[1]))

                    # Get embedding from InsightFace
                    if hasattr(largest_face, 'embedding') and largest_face.embedding is not None:
                        # Normalize the embedding
                        embedding = largest_face.embedding / np.linalg.norm(largest_face.embedding)
                        logger.info("Successfully extracted face embedding using InsightFace")
                        return embedding.tolist()
                    else:
                        logger.warning("InsightFace detected face but no embedding available")
                        return None
                else:
                    logger.warning("No faces detected by InsightFace")
                    return None
            except Exception as e:
                logger.error(f"Error using InsightFace for embedding: {e}")
                logger.error("Failed to extract embedding with InsightFace")
                return None
        else:
            logger.error("Failed to initialize InsightFace")
            return None
    except Exception as e:
        logger.error(f"Error extracting face embedding: {e}")
        return None

def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    """Calculate cosine similarity between two vectors.

    Args:
        a: First vector
        b: Second vector

    Returns:
        Cosine similarity value between 0 and 1
    """
    try:
        if a is None or b is None:
            logger.warning("Received None vector in cosine_similarity")
            return 0.0

        # Convert to numpy arrays if they aren't already
        if not isinstance(a, np.ndarray):
            a = np.array(a)
        if not isinstance(b, np.ndarray):
            b = np.array(b)

        # Log vector information
        logger.info(f"Vector shapes: a={a.shape}, b={b.shape}")

        # Ensure vectors have the same shape
        if a.shape != b.shape:
            logger.warning(f"Shape mismatch in cosine_similarity: {a.shape} vs {b.shape}")
            # Try to reshape if possible
            if len(a.shape) == 1 and len(b.shape) == 1:
                # If they're both 1D arrays, we can try to reshape
                if a.shape[0] > b.shape[0]:
                    a = a[:b.shape[0]]
                    logger.info(f"Reshaped a to {a.shape}")
                else:
                    b = b[:a.shape[0]]
                    logger.info(f"Reshaped b to {b.shape}")
            else:
                logger.error("Cannot reshape vectors with different dimensions")
                return 0.0

        # Calculate dot product
        dot_product = np.dot(a, b)

        # Calculate magnitudes
        norm_a = np.linalg.norm(a)
        norm_b = np.linalg.norm(b)

        # Avoid division by zero
        if norm_a == 0 or norm_b == 0:
            logger.warning("Zero norm detected in cosine_similarity")
            return 0.0

        # Calculate cosine similarity
        similarity = dot_product / (norm_a * norm_b)

        # Ensure result is in valid range (0 to 1)
        result = max(0.0, min(1.0, similarity))
        logger.info(f"Cosine similarity: {result:.4f}")
        return result
    except Exception as e:
        logger.error(f"Error in cosine_similarity: {e}")
        return 0.0

def start_background_processing():
    """Start background thread for face processing"""
    global processing_thread, processing_active

    if processing_active:
        return

    def process_queue():
        while processing_active:
            try:
                # Get item from queue with timeout
                item = processing_queue.get(timeout=1.0)
                if item is None:
                    continue

                # Process the item (frame, callback, args)
                frame, callback, args = item
                result = process_frame_for_recognition(frame)
                if result and callback:
                    callback(result, *args)

                # Mark task as done
                processing_queue.task_done()
            except queue.Empty:
                # Queue is empty, just continue
                pass
            except Exception as e:
                logger.error(f"Error in background processing: {e}")

    processing_active = True
    processing_thread = threading.Thread(target=process_queue, daemon=True)
    processing_thread.start()
    logger.info("Started background processing thread")

def stop_background_processing():
    """Stop background processing thread"""
    global processing_active
    processing_active = False
    if processing_thread:
        processing_thread.join(timeout=2.0)
        logger.info("Stopped background processing thread")

def process_frame_for_recognition(frame: np.ndarray) -> Optional[Tuple[int, float]]:
    """Process a frame for face recognition.

    Args:
        frame: Input frame as numpy array

    Returns:
        Tuple of (student_id, confidence) or None if no face recognized
    """
    try:
        if frame is None:
            return None

        # Preprocess image
        processed_frame = preprocess_image(frame)

        # Detect faces
        faces = detect_faces(processed_frame)
        if len(faces) == 0:
            return None

        # Get the largest face
        if len(faces) > 1:
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            face_rect = largest_face
        else:
            face_rect = faces[0]

        # Extract the face region
        x, y, w, h = face_rect
        face_region = processed_frame[y:y+h, x:x+w]

        # Resize to a standard size
        face_region = cv2.resize(face_region, (100, 100))

        # Convert to grayscale
        gray_face = cv2.cvtColor(face_region, cv2.COLOR_BGR2GRAY)

        # Flatten the image to create a simple embedding
        embedding = gray_face.flatten().astype(float)

        # Normalize the embedding
        embedding = embedding / np.linalg.norm(embedding)

        # Get all students from database
        from src.models.db import db, Student
        students = db.session.query(Student).filter(Student.face_embedding.isnot(None)).all()

        # Find best match
        best_sim = -1
        best_student = None
        second_best_sim = -1

        for student in students:
            try:
                # Get student embedding
                student_embedding = student.get_embedding()
                if student_embedding is None:
                    continue

                # Calculate similarity
                sim = cosine_similarity(embedding, student_embedding)

                # Track best and second-best matches
                if sim > best_sim:
                    second_best_sim = best_sim
                    best_sim = sim
                    best_student = student
                elif sim > second_best_sim:
                    second_best_sim = sim
            except Exception as e:
                logger.error(f"Error comparing with student {student.student_code}: {e}")

        # Check if similarity is above threshold
        if best_student and best_sim > FACE_RECOGNITION_THRESHOLD:
            # Additional verification: check if there's a clear winner
            # If the difference between best and second-best is small, it might be uncertain
            if (best_sim - second_best_sim) < VERIFICATION_THRESHOLD:
                logger.warning(f"Uncertain recognition: best={best_sim:.4f}, second={second_best_sim:.4f}, diff={best_sim-second_best_sim:.4f}")
                # For uncertain matches, require a higher threshold
                if best_sim > (FACE_RECOGNITION_THRESHOLD + 0.1):  # Higher threshold for uncertain matches
                    logger.info(f"Recognized student despite uncertainty: {best_student.student_code} (similarity: {best_sim:.4f})")
                    return (best_student.id, best_sim)
                else:
                    return None
            else:
                logger.info(f"Recognized student: {best_student.student_code} (similarity: {best_sim:.4f})")
                return (best_student.id, best_sim)
        else:
            return None
    except Exception as e:
        logger.error(f"Error in face recognition: {e}")
        return None
    finally:
        # Clean up
        if 'faces' in locals():
            del faces
        if 'embedding' in locals():
            del embedding
        gc.collect()

def recognize_face(frame, students):
    """Recognize a face in the frame by comparing with student embeddings.

    Args:
        frame: Input frame as numpy array
        students: List of Student objects to compare against

    Returns:
        Tuple of (student_id, confidence) or None if no face recognized
    """
    try:
        if frame is None:
            return None

        # Preprocess image
        processed_frame = preprocess_image(frame)

        # Make sure InsightFace is initialized
        global app
        if app is None:
            logger.warning("InsightFace not initialized in recognize_face, attempting to initialize...")
            app = initialize_insightface()
            if app is None:
                logger.error("Failed to initialize InsightFace in recognize_face")
                return None

        # Use InsightFace for face recognition
        if app is not None:
            try:
                # Detect faces using InsightFace
                faces = app.get(processed_frame)

                if not faces or len(faces) == 0:
                    logger.warning("No faces detected in frame")
                    return None

                # Get the largest face
                largest_face = max(faces, key=lambda face: (face.bbox[2] - face.bbox[0]) * (face.bbox[3] - face.bbox[1]))

                # Get embedding directly from InsightFace
                if not hasattr(largest_face, 'embedding') or largest_face.embedding is None:
                    logger.warning("Face detected but no embedding available")
                    return None

                # Normalize the embedding
                face_embedding = largest_face.embedding / np.linalg.norm(largest_face.embedding)

                # Find best match among students
                best_sim = -1
                best_student = None
                second_best_sim = -1

                for student in students:
                    try:
                        # Get student embedding
                        student_embedding = student.get_embedding()
                        if student_embedding is None:
                            logger.warning(f"Student {student.student_code} has no embedding")
                            continue

                        # Calculate similarity
                        sim = cosine_similarity(face_embedding, student_embedding)
                        logger.info(f"Similarity with student {student.student_code}: {sim:.4f}")

                        # Track best and second-best matches
                        if sim > best_sim:
                            second_best_sim = best_sim
                            best_sim = sim
                            best_student = student
                        elif sim > second_best_sim:
                            second_best_sim = sim
                    except Exception as e:
                        logger.error(f"Error comparing with student {student.student_code}: {e}")

                # Use a lower threshold for better recognition with InsightFace
                threshold = 0.3

                # Check if we have a good match
                if best_student and best_sim > threshold:
                    # If second best is close, we might have an ambiguous match
                    if second_best_sim > 0 and (best_sim - second_best_sim) < 0.1:
                        logger.warning("Ambiguous match - second best match too close")
                        return None

                    logger.info(f"Recognized student {best_student.student_code} with confidence {best_sim:.4f}")
                    return best_student.id, best_sim
                else:
                    logger.info("No good match found")
                    return None

            except Exception as e:
                logger.error(f"Error in face recognition: {e}")
                return None
        else:
            logger.error("InsightFace not initialized")
            return None

    except Exception as e:
        logger.error(f"Error in recognize_face: {e}")
        return None

def register_student_with_embedding(session, student, images):
    """Register a new student and store the average face embedding.

    Args:
        session: Database session
        student: Student object to register
        images: List of face images

    Returns:
        bool: True if registration was successful, False otherwise
    """
    try:
        if not images:
            raise ValueError("No images provided for registration")

        logger.info(f"Registering student {student.student_code} with {len(images)} images")

        # Extract embeddings from provided images
        embeddings = []
        for i, img in enumerate(images):
            # Preprocess image for better detection
            processed_img = preprocess_image(img)

            # Extract embedding
            emb = extract_face_embedding(processed_img)
            if emb:
                embeddings.append(emb)
                logger.debug(f"Extracted embedding from image {i+1}")
            else:
                logger.warning(f"Could not extract embedding from image {i+1}")

        if not embeddings:
            raise ValueError("No valid face embeddings found for registration")

        # Calculate average embedding
        avg_embedding = np.mean(np.array(embeddings), axis=0)

        # Store embedding in the new ARRAY(Float) column
        student.set_embedding(avg_embedding)

        # Also store face images with their embeddings
        from src.models.db import FaceImage
        for i, (img, emb) in enumerate(zip(images, embeddings)):
            # Convert image to bytes for storage
            _, img_bytes = cv2.imencode('.jpg', img)

            # Create face image record
            face_image = FaceImage(
                student_id=student.id,
                image_data=img_bytes.tobytes()
            )
            face_image.set_embedding(np.array(emb))
            session.add(face_image)

        # Commit changes
        session.commit()

        # Clear embedding cache
        _get_cached_embedding.cache_clear()

        logger.info(f"Successfully registered student {student.student_code} with {len(embeddings)} valid embeddings")
        return True
    except Exception as e:
        logger.error(f"Error in register_student_with_embedding: {e}")
        session.rollback()
        return False
    finally:
        # Clean up
        if 'embeddings' in locals():
            del embeddings
        if 'avg_embedding' in locals():
            del avg_embedding
        gc.collect()

def detect_screen_glare(image):
    """
    Advanced spoofing detection for screens, photos, and videos.
    Handles cases with no glare, printed photos, and moving videos.

    Args:
        image: Input image (numpy array)

    Returns:
        bool: True if spoofing detected (screen/photo/video)
    """
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY) if len(image.shape) == 3 else image
        h, w = gray.shape

        # 1. PAPER PHOTO DETECTION - Check for paper texture and print artifacts
        # Paper has specific texture patterns and print dots
        kernel = np.array([[-1,-1,-1], [-1,8,-1], [-1,-1,-1]])
        sharpened = cv2.filter2D(gray, -1, kernel)
        paper_texture_score = np.var(sharpened)

        # Paper photos have characteristic texture variance
        if 200 < paper_texture_score < 800:
            # Additional check for print dot patterns (halftone)
            blur = cv2.GaussianBlur(gray, (3, 3), 0)
            diff = cv2.absdiff(gray, blur)
            dot_pattern_score = np.mean(diff)

            if dot_pattern_score > 3.0:
                logger.warning(f"Paper photo detected: texture={paper_texture_score:.1f}, dots={dot_pattern_score:.2f}")
                return True

        # 2. SCREEN PIXEL DETECTION - Even without glare, screens have pixel structure
        # Detect sub-pixel patterns using high-frequency analysis
        laplacian = cv2.Laplacian(gray, cv2.CV_64F, ksize=1)
        high_freq_energy = np.var(laplacian)

        # Screens have very specific high-frequency characteristics (more lenient)
        if high_freq_energy < 20 or high_freq_energy > 5000:
            # Too smooth (screen) or too noisy (digital artifacts)

            # Additional check for digital compression artifacts (more lenient)
            dct = cv2.dct(np.float32(gray))
            dct_energy = np.sum(np.abs(dct[8:, 8:]))  # High frequency DCT coefficients

            if dct_energy > 3000000:  # Much higher threshold for digital compression artifacts
                logger.warning(f"Digital artifacts detected: high_freq={high_freq_energy:.1f}, dct={dct_energy:.0f}")
                return True

        # 3. SMART RECTANGULAR BOUNDARY DETECTION - Distinguish screens from natural edges
        edges = cv2.Canny(gray, 30, 100)  # Lower thresholds for subtle edges

        # Use Hough lines to detect straight edges (screens/photos have straight edges)
        lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=min(50, max(20, h//10)))

        if lines is not None and len(lines) > 10:  # Increased threshold to avoid false positives
            # Count horizontal and vertical lines
            horizontal_lines = 0
            vertical_lines = 0

            for line in lines:
                rho, theta = line[0]
                angle = theta * 180 / np.pi

                if abs(angle) < 10 or abs(angle - 180) < 10:  # Horizontal
                    horizontal_lines += 1
                elif abs(angle - 90) < 10:  # Vertical
                    vertical_lines += 1

            # Only flag as screen if there are MANY straight edges (indicating a device frame)
            # Real faces may have some straight edges but not as many as phone screens
            if horizontal_lines >= 8 and vertical_lines >= 8:
                # Additional check: verify these are actually device boundaries
                edge_density = np.sum(edges > 0) / (h * w)
                if edge_density > 0.1:  # High edge density = likely device frame
                    logger.warning(f"Screen device detected: h_lines={horizontal_lines}, v_lines={vertical_lines}, edge_density={edge_density:.3f}")
                    return True

        # 4. DEPTH ANALYSIS - Real faces have 3D depth, photos/screens are flat
        # Use gradient magnitude to detect depth cues
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

        # Calculate depth variance (real faces have varied depth gradients)
        depth_variance = np.var(gradient_magnitude)
        mean_gradient = np.mean(gradient_magnitude)

        # Flat surfaces (photos/screens) have uniform depth characteristics
        if depth_variance < 100 and mean_gradient < 20:
            logger.warning(f"Flat surface detected: depth_var={depth_variance:.1f}, mean_grad={mean_gradient:.1f}")
            return True

        # 5. COLOR SPACE ANALYSIS - Screens and photos have different color characteristics
        if len(image.shape) == 3:
            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)
            h_channel, s_channel, v_channel = cv2.split(hsv)

            # Check saturation distribution (screens often oversaturate)
            sat_mean = np.mean(s_channel)
            sat_std = np.std(s_channel)

            # Screens often have artificial saturation patterns
            if sat_mean > 120 and sat_std < 30:
                logger.warning(f"Artificial saturation detected: mean={sat_mean:.1f}, std={sat_std:.1f}")
                return True

            # Check for color banding (digital displays)
            hist_h = cv2.calcHist([h_channel], [0], None, [180], [0, 180])
            hist_peaks = np.sum(hist_h > np.mean(hist_h) * 3)

            if hist_peaks > 20:  # Too many color peaks = digital processing
                logger.warning(f"Color banding detected: peaks={hist_peaks}")
                return True

        # 6. TEMPORAL CONSISTENCY CHECK - For video detection
        # This will be enhanced in the motion detection function

        # 7. LIGHTING ANALYSIS - Screens emit light, photos reflect light (DISABLED - too aggressive)
        # Check for unnatural lighting patterns
        brightness_hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        brightness_peaks = np.sum(brightness_hist > np.max(brightness_hist) * 0.1)

        # Screens often have specific brightness distributions
        # DISABLED: This check is too aggressive for webcam conditions
        # if brightness_peaks < 10 or brightness_peaks > 250:  # More lenient thresholds
        #     brightness_uniformity = np.std(brightness_hist)
        #
        #     if brightness_uniformity > 8000:  # Much higher threshold
        #         logger.warning(f"Unnatural lighting detected: peaks={brightness_peaks}, uniformity={brightness_uniformity:.0f}")
        #         return True

        return False

    except Exception as e:
        logger.error(f"Error in advanced spoofing detection: {e}")
        return False

def analyze_face_texture(face_region):
    """
    Analyze face texture to detect flat surfaces (photos/screens).
    Enhanced to detect digital display characteristics.

    Args:
        face_region: Cropped face region (numpy array)

    Returns:
        float: Texture score (higher = more natural texture)
    """
    try:
        # Convert to grayscale
        gray = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY) if len(face_region.shape) == 3 else face_region

        # 1. Enhanced Local Binary Pattern (LBP) analysis
        h, w = gray.shape
        if h < 10 or w < 10:  # Face too small for analysis
            return 0.1

        lbp_image = np.zeros_like(gray)

        for i in range(1, h-1):
            for j in range(1, w-1):
                center = gray[i, j]
                code = 0
                code |= (gray[i-1, j-1] >= center) << 7
                code |= (gray[i-1, j] >= center) << 6
                code |= (gray[i-1, j+1] >= center) << 5
                code |= (gray[i, j+1] >= center) << 4
                code |= (gray[i+1, j+1] >= center) << 3
                code |= (gray[i+1, j] >= center) << 2
                code |= (gray[i+1, j-1] >= center) << 1
                code |= (gray[i, j-1] >= center) << 0
                lbp_image[i, j] = code

        # Calculate texture uniformity
        hist, _ = np.histogram(lbp_image.ravel(), bins=256, range=(0, 256))
        hist = hist.astype(float)
        hist /= (hist.sum() + 1e-6)

        # Calculate entropy (measure of texture complexity)
        entropy = -np.sum(hist * np.log2(hist + 1e-6))
        base_texture_score = entropy / 8.0  # Normalize to 0-1 range

        # 2. Gradient analysis for screen detection
        # Screens often have artificial gradients
        grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
        grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)

        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        gradient_variance = np.var(gradient_magnitude)

        # Natural skin has more varied gradients than screen displays
        if gradient_variance < 50.0:  # Too uniform gradients = likely screen
            base_texture_score *= 0.5

        # 3. Pixel intensity distribution analysis
        # Screens often have quantized intensity levels
        intensity_hist, _ = np.histogram(gray.ravel(), bins=32, range=(0, 256))
        intensity_peaks = np.sum(intensity_hist > np.mean(intensity_hist) * 2)

        # Too many intensity peaks = likely digital quantization
        if intensity_peaks > 8:  # More than 8 prominent peaks suggests digital processing
            base_texture_score *= 0.7

        # 4. High-frequency noise analysis
        # Natural skin has micro-textures, screens are too smooth
        laplacian = cv2.Laplacian(gray, cv2.CV_64F)
        high_freq_energy = np.var(laplacian)

        # Very low high-frequency energy = too smooth = likely screen
        if high_freq_energy < 100.0:
            base_texture_score *= 0.6

        # 5. Color channel correlation (if color image available)
        if len(face_region.shape) == 3:
            b, g, r = cv2.split(face_region)

            # Calculate correlation between color channels
            corr_bg = np.corrcoef(b.ravel(), g.ravel())[0, 1]
            corr_gr = np.corrcoef(g.ravel(), r.ravel())[0, 1]
            corr_rb = np.corrcoef(r.ravel(), b.ravel())[0, 1]

            avg_correlation = np.mean([abs(corr_bg), abs(corr_gr), abs(corr_rb)])

            # Too high correlation between channels = artificial/screen
            if avg_correlation > 0.9:
                base_texture_score *= 0.5

        # Final texture score with penalties for screen-like characteristics
        final_score = max(0.0, min(1.0, base_texture_score))

        return final_score

    except Exception as e:
        logger.error(f"Error in texture analysis: {e}")
        return 0.2  # Lower default score to be more conservative

def detect_motion_liveness(frame_history):
    """
    Advanced motion analysis to detect natural vs artificial movement.
    Detects video playback, including subtle movements.

    Args:
        frame_history: List of recent frames

    Returns:
        bool: True if natural motion detected, False if artificial/video
    """
    try:
        if len(frame_history) < 5:
            return True  # Not enough frames, assume live for now

        motion_scores = []
        optical_flow_scores = []
        temporal_consistency_scores = []

        for i in range(1, len(frame_history)):
            prev_frame = cv2.cvtColor(frame_history[i-1], cv2.COLOR_RGB2GRAY) if len(frame_history[i-1].shape) == 3 else frame_history[i-1]
            curr_frame = cv2.cvtColor(frame_history[i], cv2.COLOR_RGB2GRAY) if len(frame_history[i].shape) == 3 else frame_history[i]

            # 1. Basic frame difference
            diff = cv2.absdiff(prev_frame, curr_frame)
            motion_score = np.mean(diff)
            motion_scores.append(motion_score)

            # 2. Optical flow analysis for motion patterns
            try:
                # Calculate dense optical flow
                flow = cv2.calcOpticalFlowPyrLK(
                    prev_frame, curr_frame,
                    np.array([[100, 100]], dtype=np.float32),  # Single point for simplicity
                    None
                )
                if flow[0] is not None:
                    flow_magnitude = np.linalg.norm(flow[0] - np.array([[100, 100]]))
                    optical_flow_scores.append(flow_magnitude)
            except:
                optical_flow_scores.append(0)

            # 3. Temporal consistency check (videos have compression artifacts)
            # Check for periodic patterns that indicate video compression
            if i >= 3:
                recent_diffs = motion_scores[-3:]
                consistency = np.std(recent_diffs)
                temporal_consistency_scores.append(consistency)

        # Analyze motion characteristics
        avg_motion = np.mean(motion_scores)
        motion_variance = np.var(motion_scores)

        # VIDEO DETECTION PATTERNS:

        # 1. Check for too-regular motion (video loop patterns)
        if len(motion_scores) >= 4:
            # Look for repeating patterns (video loops)
            motion_pattern = np.array(motion_scores)
            autocorr = np.correlate(motion_pattern, motion_pattern, mode='full')
            autocorr_normalized = autocorr / np.max(autocorr)

            # High autocorrelation indicates repeating patterns (video)
            peak_indices = np.where(autocorr_normalized > 0.8)[0]
            if len(peak_indices) > 3:
                logger.warning(f"Repeating motion pattern detected - likely video loop")
                return False

        # 2. Check for artificial motion smoothness (video interpolation)
        if len(optical_flow_scores) > 0:
            flow_variance = np.var(optical_flow_scores)
            avg_flow = np.mean(optical_flow_scores)

            # Videos often have smooth, artificial motion
            if flow_variance < 0.1 and avg_flow > 0.5:
                logger.warning(f"Artificial motion smoothness detected - likely video")
                return False

        # 3. Check for compression artifacts in motion
        if len(temporal_consistency_scores) > 0:
            consistency_mean = np.mean(temporal_consistency_scores)

            # Video compression creates specific temporal patterns
            if consistency_mean < 0.5:  # Too consistent = compressed video
                logger.warning(f"Video compression artifacts detected")
                return False

        # 4. NATURAL MOTION VALIDATION
        # Real human micro-movements have specific characteristics

        # Check for natural breathing/pulse patterns
        if len(motion_scores) >= 8:
            # Look for subtle periodic motion (breathing, pulse)
            fft = np.fft.fft(motion_scores)
            frequencies = np.fft.fftfreq(len(motion_scores))

            # Human breathing: 0.2-0.5 Hz, pulse: 1-2 Hz
            breathing_range = np.where((frequencies > 0.1) & (frequencies < 0.6))[0]
            pulse_range = np.where((frequencies > 0.8) & (frequencies < 2.5))[0]

            breathing_energy = np.sum(np.abs(fft[breathing_range])) if len(breathing_range) > 0 else 0
            pulse_energy = np.sum(np.abs(fft[pulse_range])) if len(pulse_range) > 0 else 0

            # Natural motion should have some biological patterns
            if breathing_energy > 0 or pulse_energy > 0:
                # Has natural biological motion patterns
                pass
            else:
                # No biological patterns - might be artificial
                if avg_motion > 5.0:  # But has significant motion
                    logger.warning(f"Motion without biological patterns - likely video")
                    return False

        # 5. FINAL MOTION CLASSIFICATION

        # Static image detection
        if avg_motion < 1.0:
            logger.warning(f"No motion detected - likely static photo")
            return False

        # Excessive motion detection (video playback)
        if avg_motion > 25.0:
            logger.warning(f"Excessive motion detected - likely video playback")
            return False

        # Unnatural motion variance
        if motion_variance < 0.5:  # Too consistent
            logger.warning(f"Unnatural motion consistency - likely video")
            return False

        if motion_variance > 50.0:  # Too chaotic
            logger.warning(f"Chaotic motion detected - likely video artifacts")
            return False

        # Motion appears natural
        return True

    except Exception as e:
        logger.error(f"Error in advanced motion detection: {e}")
        return True  # Default to allowing motion if analysis fails

def validate_face_quality(face_bbox, image):
    """
    Validate face quality and size for liveness detection.

    Args:
        face_bbox: Face bounding box [x, y, w, h]
        image: Input image

    Returns:
        dict: Quality metrics
    """
    try:
        x, y, w, h = face_bbox

        # Check face size
        face_size = min(w, h)
        size_valid = MIN_FACE_SIZE <= face_size <= MAX_FACE_SIZE

        # Extract face region
        face_region = image[y:y+h, x:x+w]

        # Check face quality (sharpness)
        gray_face = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY) if len(face_region.shape) == 3 else face_region
        laplacian_var = cv2.Laplacian(gray_face, cv2.CV_64F).var()
        sharpness_score = min(laplacian_var / 1000.0, 1.0)  # Normalize

        # Check brightness
        brightness = np.mean(gray_face)
        brightness_valid = 50 < brightness < 200  # Avoid too dark or too bright

        return {
            'size_valid': size_valid,
            'face_size': face_size,
            'sharpness_score': sharpness_score,
            'brightness': brightness,
            'brightness_valid': brightness_valid,
            'quality_score': sharpness_score if size_valid and brightness_valid else 0.0
        }

    except Exception as e:
        logger.error(f"Error in face quality validation: {e}")
        return {
            'size_valid': False,
            'face_size': 0,
            'sharpness_score': 0.0,
            'brightness': 0,
            'brightness_valid': False,
            'quality_score': 0.0
        }

def detect_3d_face_structure(face_region):
    """
    Analyze 3D face structure to detect flat photos/screens.
    Real faces have 3D depth, photos/screens are flat.

    Args:
        face_region: Cropped face region

    Returns:
        dict: 3D analysis results
    """
    try:
        gray = cv2.cvtColor(face_region, cv2.COLOR_RGB2GRAY) if len(face_region.shape) == 3 else face_region
        h, w = gray.shape

        if h < 50 or w < 50:
            return {'is_3d': False, 'confidence': 0.0, 'reason': 'Face too small for 3D analysis'}

        # 1. Nose bridge detection (3D feature)
        # Real faces have a prominent nose bridge with specific shadow patterns
        center_x, center_y = w // 2, h // 2
        nose_region = gray[center_y-h//6:center_y+h//6, center_x-w//8:center_x+w//8]

        if nose_region.size > 0:
            # Calculate gradient in nose region
            grad_x = cv2.Sobel(nose_region, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(nose_region, cv2.CV_64F, 0, 1, ksize=3)

            # Nose bridge should have strong vertical gradients
            vertical_gradient_strength = np.mean(np.abs(grad_y))
            horizontal_gradient_strength = np.mean(np.abs(grad_x))

            nose_3d_score = vertical_gradient_strength / (horizontal_gradient_strength + 1e-6)

            if nose_3d_score < 0.8:  # Flat nose = likely photo
                return {'is_3d': False, 'confidence': 0.2, 'reason': f'Flat nose detected: {nose_3d_score:.2f}'}

        # 2. Cheek depth analysis
        # Real faces have curved cheeks with depth gradients
        left_cheek = gray[center_y-h//4:center_y+h//4, center_x-w//3:center_x-w//6]
        right_cheek = gray[center_y-h//4:center_y+h//4, center_x+w//6:center_x+w//3]

        cheek_depth_scores = []
        for cheek in [left_cheek, right_cheek]:
            if cheek.size > 0:
                # Calculate curvature using second derivatives
                laplacian = cv2.Laplacian(cheek, cv2.CV_64F)
                curvature_score = np.var(laplacian)
                cheek_depth_scores.append(curvature_score)

        avg_cheek_depth = np.mean(cheek_depth_scores) if cheek_depth_scores else 0

        if avg_cheek_depth < 50:  # Too flat = likely photo
            return {'is_3d': False, 'confidence': 0.3, 'reason': f'Flat cheeks detected: {avg_cheek_depth:.1f}'}

        # 3. Eye socket depth analysis
        # Real eyes are set in sockets with shadows
        eye_y = center_y - h//4
        left_eye_region = gray[eye_y-h//8:eye_y+h//8, center_x-w//3:center_x-w//6]
        right_eye_region = gray[eye_y-h//8:eye_y+h//8, center_x+w//6:center_x+w//3]

        eye_depth_scores = []
        for eye_region in [left_eye_region, right_eye_region]:
            if eye_region.size > 0:
                # Look for shadow patterns around eyes
                mean_brightness = np.mean(eye_region)
                brightness_variance = np.var(eye_region)

                # Real eyes have shadows and depth
                if mean_brightness > 150 or brightness_variance < 100:
                    eye_depth_scores.append(0)  # Too bright or uniform = flat
                else:
                    eye_depth_scores.append(1)  # Has depth characteristics

        eye_depth_score = np.mean(eye_depth_scores) if eye_depth_scores else 0

        if eye_depth_score < 0.5:
            return {'is_3d': False, 'confidence': 0.4, 'reason': 'Flat eye regions detected'}

        # 4. Overall 3D confidence
        confidence_3d = min(1.0, (nose_3d_score + avg_cheek_depth/100 + eye_depth_score) / 3)

        return {
            'is_3d': confidence_3d > 0.6,
            'confidence': confidence_3d,
            'reason': f'3D face analysis: {confidence_3d:.2f}'
        }

    except Exception as e:
        logger.error(f"Error in 3D face analysis: {e}")
        return {'is_3d': False, 'confidence': 0.0, 'reason': f'3D analysis failed: {str(e)}'}

def comprehensive_liveness_check(image, face_bbox, frame_history=None):
    """
    Comprehensive liveness detection combining multiple advanced techniques.
    Handles photos, screens, videos, and sophisticated spoofing attempts.

    Args:
        image: Current frame
        face_bbox: Face bounding box [x, y, w, h]
        frame_history: List of recent frames for motion analysis

    Returns:
        dict: Liveness analysis results
    """
    try:
        if not LIVENESS_ENABLED:
            return {
                'is_live': True,
                'confidence': 1.0,
                'reason': 'Liveness detection disabled'
            }

        x, y, w, h = face_bbox
        face_region = image[y:y+h, x:x+w]

        # 1. Advanced spoofing detection (screens, photos, videos)
        has_spoofing = detect_screen_glare(image)  # Now handles all spoofing types
        if has_spoofing:
            return {
                'is_live': False,
                'confidence': 0.1,
                'reason': 'Spoofing detected - photo/video/screen identified'
            }

        # 2. Face quality validation
        quality_metrics = validate_face_quality(face_bbox, image)
        if not quality_metrics['size_valid']:
            return {
                'is_live': False,
                'confidence': 0.2,
                'reason': f'Invalid face size: {quality_metrics["face_size"]}px (min: {MIN_FACE_SIZE}, max: {MAX_FACE_SIZE})'
            }

        if quality_metrics['quality_score'] < FACE_QUALITY_THRESHOLD:
            return {
                'is_live': False,
                'confidence': 0.3,
                'reason': f'Poor face quality: {quality_metrics["quality_score"]:.2f} < {FACE_QUALITY_THRESHOLD}'
            }

        # 3. Enhanced texture analysis
        texture_score = analyze_face_texture(face_region)
        if texture_score < TEXTURE_ANALYSIS_THRESHOLD:
            return {
                'is_live': False,
                'confidence': 0.4,
                'reason': f'Artificial surface detected - texture score: {texture_score:.2f} < {TEXTURE_ANALYSIS_THRESHOLD}'
            }

        # 4. 3D face structure analysis
        face_3d_analysis = detect_3d_face_structure(face_region)
        if not face_3d_analysis['is_3d']:
            return {
                'is_live': False,
                'confidence': 0.5,
                'reason': f'Flat surface detected - {face_3d_analysis["reason"]}'
            }

        # 5. Advanced motion analysis (if frame history available)
        motion_detected = True
        motion_confidence = 1.0

        if frame_history and len(frame_history) >= MOTION_DETECTION_FRAMES:
            motion_detected = detect_motion_liveness(frame_history[-MOTION_DETECTION_FRAMES:])
            if not motion_detected:
                return {
                    'is_live': False,
                    'confidence': 0.6,
                    'reason': 'Artificial motion detected - likely video playback'
                }

            # Calculate motion confidence based on naturalness
            motion_scores = []
            for i in range(1, min(len(frame_history), 6)):
                prev = cv2.cvtColor(frame_history[i-1], cv2.COLOR_RGB2GRAY) if len(frame_history[i-1].shape) == 3 else frame_history[i-1]
                curr = cv2.cvtColor(frame_history[i], cv2.COLOR_RGB2GRAY) if len(frame_history[i].shape) == 3 else frame_history[i]
                diff = cv2.absdiff(prev, curr)
                motion_scores.append(np.mean(diff))

            if motion_scores:
                motion_variance = np.var(motion_scores)
                motion_confidence = min(1.0, motion_variance / 10.0)  # Normalize variance

        # 6. Calculate overall confidence with weighted factors
        confidence_factors = [
            quality_metrics['quality_score'] * 0.2,      # 20% weight
            texture_score * 0.25,                        # 25% weight
            face_3d_analysis['confidence'] * 0.25,       # 25% weight
            motion_confidence * 0.2,                     # 20% weight
            (1.0 if not has_spoofing else 0.0) * 0.1     # 10% weight
        ]

        overall_confidence = sum(confidence_factors)

        # Apply BALANCED SECURITY threshold - strict but usable
        security_threshold = LIVENESS_THRESHOLD  # Use configured threshold
        is_live = overall_confidence >= security_threshold

        # Smart security check - block only if MULTIPLE indicators suggest spoofing
        spoofing_indicators = 0
        if has_spoofing:
            spoofing_indicators += 1
        if not face_3d_analysis['is_3d']:
            spoofing_indicators += 1
        if texture_score < TEXTURE_ANALYSIS_THRESHOLD:
            spoofing_indicators += 1

        # Block only if 2 or more spoofing indicators (more balanced)
        if spoofing_indicators >= 2:
            is_live = False
            overall_confidence = 0.1  # Force very low confidence

        # Log detailed analysis for debugging
        logger.warning(f"LIVENESS CHECK DETAILS:")
        logger.warning(f"  • Overall confidence: {overall_confidence:.3f}")
        logger.warning(f"  • Security threshold: {security_threshold:.3f}")
        logger.warning(f"  • Spoofing detected: {has_spoofing}")
        logger.warning(f"  • 3D face detected: {face_3d_analysis['is_3d']}")
        logger.warning(f"  • Texture score: {texture_score:.3f} (min: {TEXTURE_ANALYSIS_THRESHOLD})")
        logger.warning(f"  • Quality score: {quality_metrics['quality_score']:.3f}")
        logger.warning(f"  • Motion detected: {motion_detected}")
        logger.warning(f"  • FINAL DECISION: {'LIVE' if is_live else 'BLOCKED'}")

        return {
            'is_live': is_live,
            'confidence': overall_confidence,
            'reason': f'MAXIMUM SECURITY: {overall_confidence:.2f} (req: {security_threshold:.2f}) - {"LIVE PERSON" if is_live else "SPOOFING BLOCKED"}',
            'details': {
                'quality_score': quality_metrics['quality_score'],
                'texture_score': texture_score,
                'face_3d_confidence': face_3d_analysis['confidence'],
                'motion_detected': motion_detected,
                'motion_confidence': motion_confidence,
                'spoofing_detected': has_spoofing,
                'face_size': quality_metrics['face_size'],
                'security_level': 'MAXIMUM',
                'threshold_used': security_threshold
            }
        }

    except Exception as e:
        logger.error(f"Error in comprehensive liveness check: {e}")
        return {
            'is_live': False,
            'confidence': 0.0,
            'reason': f'Liveness check failed: {str(e)}'
        }
#!/usr/bin/env python3
import subprocess
import os
import sys
import time
import threading
import socket
import platform

def stream_output(process, prefix):
    """Stream the output of a process with a prefix."""
    for line in iter(process.stdout.readline, ''):
        print(f"{prefix}: {line.strip()}")

def is_port_in_use(port):
    """Check if a port is in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def find_free_port(start_port=8080, end_port=9000):
    """Find a free port in the given range."""
    for port in range(start_port, end_port):
        if not is_port_in_use(port):
            return port
    raise RuntimeError(f"No free ports found in range {start_port}-{end_port}")

def print_colored(text, color):
    """Print colored text to the console."""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, '')}{text}{colors['end']}")

def is_apple_silicon():
    """Check if running on Apple Silicon (M1/M2)."""
    return platform.system() == 'Darwin' and platform.processor() == 'arm'

def setup_gpu_acceleration():
    """Setup GPU acceleration for Mac M1/M2."""
    if not is_apple_silicon():
        return False

    print_colored("Detected Apple Silicon (M1/M2)!", 'green')
    print_colored("Setting up GPU acceleration...", 'blue')

    # Get the base directory
    base_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.join(base_dir, "Face-Recognition-Attendance")
    env_file = os.path.join(project_dir, ".env")

    # Check if .env file exists
    if os.path.exists(env_file):
        # Read the .env file
        with open(env_file, 'r') as f:
            env_content = f.read()

        # Check if USE_GPU is already set
        if "USE_GPU=True" not in env_content:
            # Add USE_GPU=True to the .env file
            with open(env_file, 'a') as f:
                f.write("\n# GPU Acceleration\nUSE_GPU=True  # Enable GPU acceleration for Mac M1/M2\n")
            print_colored("Added USE_GPU=True to .env file.", 'green')

    # Check if onnxruntime with CoreML support is available
    try:
        import onnxruntime as ort

        # Check available providers
        providers = ort.get_available_providers()
        print_colored(f"Available ONNX Runtime providers: {', '.join(providers)}", 'blue')

        if 'CoreMLExecutionProvider' in providers:
            print_colored("CoreML support is available in your onnxruntime installation!", 'green')
        else:
            print_colored("CoreML provider not found in onnxruntime providers list.", 'yellow')
            print_colored("Your system will use CPU for face recognition.", 'yellow')
            print_colored("This is normal if you're using an older version of onnxruntime.", 'yellow')
    except Exception as e:
        print_colored(f"Warning: Could not check onnxruntime providers: {e}", 'yellow')
        print_colored("The system will still work but may not use GPU acceleration.", 'yellow')

    print_colored("GPU acceleration setup complete!", 'green')
    return True

def kill_process_on_port(port):
    """Kill any process using the specified port."""
    print(f"Checking for processes using port {port}...")
    try:
        # Find PID of process using the port
        result = subprocess.run(
            f"lsof -i :{port} -t",
            shell=True,
            capture_output=True,
            text=True
        )

        if result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                if pid:
                    print(f"Killing process {pid} using port {port}")
                    try:
                        # First try a graceful termination
                        subprocess.run(f"kill {pid}", shell=True)

                        # Wait a moment to see if it terminates
                        time.sleep(0.5)

                        # Check if the process is still running
                        check_result = subprocess.run(
                            f"ps -p {pid} > /dev/null",
                            shell=True
                        )

                        # If the process is still running, force kill it
                        if check_result.returncode == 0:
                            print(f"Process {pid} still running, force killing...")
                            subprocess.run(f"kill -9 {pid}", shell=True)
                    except Exception as kill_error:
                        print(f"Error killing process {pid}: {kill_error}")
                        # Try force kill as a last resort
                        subprocess.run(f"kill -9 {pid}", shell=True)

            # Double-check that the port is now free
            time.sleep(1)  # Give the OS time to release the port
            if is_port_in_use(port):
                print(f"Port {port} is still in use after killing processes. Trying again...")
                # Try one more time with a more aggressive approach
                subprocess.run(f"lsof -i :{port} -t | xargs kill -9", shell=True)
                time.sleep(1)

                if is_port_in_use(port):
                    print(f"WARNING: Port {port} is still in use. This may cause problems.")
                else:
                    print(f"Port {port} is now free after second attempt.")
            else:
                print(f"Port {port} is now free.")

            return True
        else:
            print(f"No processes found using port {port}")
            return False
    except Exception as e:
        print(f"Error killing process on port {port}: {e}")
        return False

def preload_models():
    """Preload face recognition models to avoid startup delays."""
    print_colored("🧠 Preloading face recognition models...", 'blue')
    import time
    import numpy as np

    try:
        # Add project directory to Python path
        base_dir = os.path.dirname(os.path.abspath(__file__))
        project_dir = os.path.dirname(base_dir)
        sys.path.insert(0, os.path.join(project_dir, "src"))

        start_time = time.time()

        # Import and initialize face recognition models
        from src.services.face_utils import initialize_insightface, app as face_app

        if face_app is None:
            print_colored("   ⚠️  InsightFace not initialized, attempting to initialize...", 'yellow')
            face_app = initialize_insightface()

        if face_app is not None:
            print_colored("   ✅ InsightFace model loaded successfully", 'green')

            # Warm up the model with multiple test images of different sizes
            test_sizes = [(640, 640), (480, 480), (320, 320)]

            for i, (width, height) in enumerate(test_sizes):
                print_colored(f"   🔥 Warming up with test image {i+1}/{len(test_sizes)} ({width}x{height})...", 'blue')

                # Create a test image with some variation
                test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)

                # Add some simple patterns to make it more realistic
                test_image[height//4:3*height//4, width//4:3*width//4] = 128

                try:
                    inference_start = time.time()
                    test_result = face_app.get(test_image)
                    inference_time = time.time() - inference_start
                    print_colored(f"   ✅ Test {i+1} completed in {inference_time:.3f}s - detected {len(test_result)} faces", 'green')
                except Exception as warmup_e:
                    print_colored(f"   ⚠️  Test {i+1} failed (non-critical): {warmup_e}", 'yellow')

            # Test face embedding extraction
            print_colored("   🧪 Testing face embedding extraction...", 'blue')
            try:
                from src.services.face_utils import extract_face_embedding

                # Create a test face-like image
                test_face = np.random.randint(50, 200, (100, 100, 3), dtype=np.uint8)

                embedding_start = time.time()
                embedding = extract_face_embedding(test_face)
                embedding_time = time.time() - embedding_start

                if embedding is not None:
                    print_colored(f"   ✅ Face embedding extraction test completed in {embedding_time:.3f}s", 'green')
                else:
                    print_colored("   ⚠️  Face embedding extraction returned None (expected for test image)", 'yellow')

            except Exception as e:
                print_colored(f"   ⚠️  Face embedding test failed (non-critical): {e}", 'yellow')

            total_time = time.time() - start_time
            print_colored(f"✅ Model preloading completed successfully in {total_time:.2f} seconds", 'green')
            print_colored("🎯 System is ready for face recognition!", 'green')
            return True
        else:
            print_colored("   ❌ Failed to initialize InsightFace model", 'red')
            return False

    except Exception as e:
        print_colored(f"❌ Error preloading models: {e}", 'red')
        return False

def start_servers():
    # Get the base directory
    base_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(base_dir)  # Go up one level to project root
    flask_dir = project_dir
    webrtc_dir = project_dir

    # Print welcome message
    print("\n" + "="*80)
    print_colored("Face Recognition Attendance System", 'blue')
    print("="*80 + "\n")

    # Setup GPU acceleration for Mac M1/M2 if applicable
    if is_apple_silicon():
        setup_gpu_acceleration()
        print("")  # Add a blank line for better readability

    # Preload models before starting servers
    print_colored("Initializing system components...", 'blue')
    if not preload_models():
        print_colored("⚠️  Warning: Model preloading failed, but continuing with server startup...", 'yellow')
    print("")  # Add a blank line for better readability

    # Kill any processes using port 5001 and 8001 (as per user preference)
    # Always check port 8001 even if it doesn't appear to be in use
    # This is because sometimes the OS doesn't immediately release the port
    print("Checking for processes using critical ports...")
    kill_process_on_port(5001)  # Always try to kill processes on 5001
    kill_process_on_port(8001)  # Always try to kill processes on 8001

    # Double-check that the ports are free
    for port in [5001, 8001]:
        if is_port_in_use(port):
            print_colored(f"WARNING: Port {port} is still in use after cleanup attempts.", 'yellow')
            print_colored(f"This may cause problems. Consider restarting your computer if issues persist.", 'yellow')
        else:
            print(f"Confirmed port {port} is free and available.")

    # Wait a moment to ensure ports are fully released
    time.sleep(2)

    # Find a free port for the WebRTC server
    webrtc_port = find_free_port(8080, 9000)
    print(f"Found free port for WebRTC server: {webrtc_port}")

    # Create a temporary file to store the WebRTC port
    port_file = os.path.join(flask_dir, "webrtc_port.txt")
    with open(port_file, 'w') as f:
        f.write(str(webrtc_port))

    # Start Flask app
    print("Starting Flask app...")
    flask_process = subprocess.Popen(
        ["python", "src/app/app.py"],
        cwd=flask_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        bufsize=1,
        universal_newlines=True
    )

    # Start a thread to stream Flask output
    flask_thread = threading.Thread(
        target=stream_output,
        args=(flask_process, "FLASK"),
        daemon=True
    )
    flask_thread.start()

    # Give Flask app time to start
    time.sleep(2)

    # Start WebRTC server
    print(f"Starting WebRTC server on port {webrtc_port}...")
    webrtc_process = subprocess.Popen(
        ["python", "src/services/webrtc_server.py", "--port", str(webrtc_port)],
        cwd=webrtc_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        bufsize=1,
        universal_newlines=True
    )

    # Start a thread to stream WebRTC output
    webrtc_thread = threading.Thread(
        target=stream_output,
        args=(webrtc_process, "WEBRTC"),
        daemon=True
    )
    webrtc_thread.start()

    print(f"Flask app PID: {flask_process.pid}")
    print(f"WebRTC server PID: {webrtc_process.pid}")
    print("\n" + "="*80)
    print_colored("🎉 BOTH SERVERS ARE NOW RUNNING SUCCESSFULLY!", 'green')
    print("="*80)
    print_colored("🌐 MAIN APPLICATION:", 'blue')
    print_colored(f"   • Web Interface: http://localhost:5001", 'cyan')
    print_colored(f"   • Local Network: http://127.0.0.1:5001", 'cyan')
    print("")
    print_colored("🎥 WEBRTC SERVER:", 'blue')
    print_colored(f"   • Video Processing: http://localhost:{webrtc_port}", 'cyan')
    print_colored(f"   • Local Network:    http://127.0.0.1:{webrtc_port}", 'cyan')
    print("="*80)
    print_colored("📋 AVAILABLE ROUTES:", 'blue')
    print_colored("   • Main Dashboard:     http://localhost:5001/", 'white')
    print_colored("   • Attendance System:  http://localhost:5001/attendance", 'white')
    print_colored("   • Student Management: http://localhost:5001/students", 'white')
    print_colored("   • Admin Panel:        http://localhost:5001/admin", 'white')

    print("="*80)
    # Print GPU status if on Apple Silicon
    if is_apple_silicon():
        print_colored("⚡ GPU acceleration is enabled for your Mac M1/M2", 'green')
    else:
        print_colored("💻 Running on CPU (GPU acceleration not available)", 'yellow')

    print("="*80)
    print_colored("🚀 SYSTEM READY! Click any link above to access the application", 'green')
    print_colored("🛑 Press Ctrl+C to stop both servers", 'yellow')
    print("="*80 + "\n")

    try:
        # Keep the main thread alive
        while flask_process.poll() is None and webrtc_process.poll() is None:
            time.sleep(0.5)

        # If we get here, one of the processes has ended
        if flask_process.poll() is not None:
            print("Flask app has stopped unexpectedly")
        if webrtc_process.poll() is not None:
            print("WebRTC server has stopped unexpectedly")

    except KeyboardInterrupt:
        print("\nStopping servers...")
        # Send SIGTERM to both processes
        if flask_process.poll() is None:
            flask_process.terminate()
        if webrtc_process.poll() is None:
            webrtc_process.terminate()

        # Wait for processes to terminate
        try:
            flask_process.wait(timeout=5)
            webrtc_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            print("Forcefully killing processes...")
            if flask_process.poll() is None:
                flask_process.kill()
            if webrtc_process.poll() is None:
                webrtc_process.kill()

        print("Servers stopped")

if __name__ == "__main__":
    start_servers()

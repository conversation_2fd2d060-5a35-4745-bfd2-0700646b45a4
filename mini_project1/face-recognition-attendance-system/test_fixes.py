#!/usr/bin/env python3
"""
Test script to verify all the fixes are working properly.
"""

import os
import sys

def test_environment_variables():
    """Test that environment variables are set correctly."""
    print("🔍 Testing environment variables...")
    
    # Load .env file
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check for NO_ALBUMENTATIONS_UPDATE
    no_update = os.getenv('NO_ALBUMENTATIONS_UPDATE')
    if no_update == '1':
        print("   ✅ NO_ALBUMENTATIONS_UPDATE is set correctly")
    else:
        print("   ❌ NO_ALBUMENTATIONS_UPDATE is not set")
    
    return no_update == '1'

def test_socketio_import():
    """Test that SocketIO imports correctly with eventlet."""
    print("🔍 Testing SocketIO and eventlet...")
    
    try:
        import eventlet
        print("   ✅ eventlet imported successfully")
        
        from flask_socketio import SocketIO
        print("   ✅ flask_socketio imported successfully")
        
        # Test creating a SocketIO instance with eventlet
        from flask import Flask
        test_app = Flask(__name__)
        test_socketio = SocketIO(test_app, async_mode='eventlet')
        print("   ✅ SocketIO with eventlet mode created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ SocketIO/eventlet test failed: {e}")
        return False

def test_flask_limiter():
    """Test Flask-Limiter configuration."""
    print("🔍 Testing Flask-Limiter...")
    
    try:
        from flask import Flask
        from flask_limiter import Limiter
        from flask_limiter.util import get_remote_address
        
        test_app = Flask(__name__)
        test_limiter = Limiter(
            app=test_app,
            key_func=get_remote_address,
            default_limits=["1000 per day", "500 per hour"],
            storage_uri="memory://"
        )
        print("   ✅ Flask-Limiter with memory storage created successfully")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Flask-Limiter test failed: {e}")
        return False

def test_face_recognition_imports():
    """Test that face recognition modules import correctly."""
    print("🔍 Testing face recognition imports...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), "src"))
        
        from src.services.face_utils import initialize_insightface
        print("   ✅ face_utils imported successfully")
        
        # Test model initialization (this might take a moment)
        print("   🧠 Testing model initialization...")
        app = initialize_insightface()
        
        if app is not None:
            print("   ✅ InsightFace model initialized successfully")
            return True
        else:
            print("   ⚠️  InsightFace model returned None (might be expected)")
            return True  # This is not necessarily an error
            
    except Exception as e:
        print(f"   ❌ Face recognition test failed: {e}")
        return False

def test_albumentations():
    """Test albumentations import without update warning."""
    print("🔍 Testing albumentations...")
    
    try:
        # Set environment variable to suppress update check
        os.environ['NO_ALBUMENTATIONS_UPDATE'] = '1'
        
        import albumentations
        print(f"   ✅ albumentations imported successfully (version: {albumentations.__version__})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ albumentations test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🎯 Testing All Fixes Applied to Face Recognition System")
    print("=" * 60)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("SocketIO & Eventlet", test_socketio_import),
        ("Flask-Limiter", test_flask_limiter),
        ("Albumentations", test_albumentations),
        ("Face Recognition", test_face_recognition_imports),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All fixes are working correctly!")
        print("🚀 You can now run the system with: python main.py --mode app")
    else:
        print("⚠️  Some issues remain. Check the failed tests above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

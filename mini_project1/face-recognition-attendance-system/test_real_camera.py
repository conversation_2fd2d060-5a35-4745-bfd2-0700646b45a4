#!/usr/bin/env python3
"""
Real camera testing tool for liveness detection.
Test with actual webcam in real environment conditions.
"""

import sys
import os
import cv2
import numpy as np
import time

# Add the src directory to the path
sys.path.append('src')

def test_real_camera_liveness():
    """Test liveness detection with real camera feed."""
    print("\n🎥 REAL CAMERA LIVENESS TESTING")
    print("=" * 60)
    
    try:
        from src.services.face_utils import (
            comprehensive_liveness_check,
            detect_faces,
            LIVENESS_THRESHOLD,
            TEXTURE_ANALYSIS_THRESHOLD,
            FACE_QUALITY_THRESHOLD
        )
        
        print("✅ Successfully imported liveness detection functions")
        print(f"📊 Current thresholds:")
        print(f"   • LIVENESS_THRESHOLD: {LIVENESS_THRESHOLD}")
        print(f"   • TEXTURE_ANALYSIS_THRESHOLD: {TEXTURE_ANALYSIS_THRESHOLD}")
        print(f"   • FACE_QUALITY_THRESHOLD: {FACE_QUALITY_THRESHOLD}")
        
        # Initialize camera
        print(f"\n📹 Initializing camera...")
        cap = cv2.VideoCapture(0)
        
        if not cap.isOpened():
            print("❌ Error: Could not open camera")
            return False
        
        # Set camera properties for better quality
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        print("✅ Camera initialized successfully")
        print("\n🔍 TESTING INSTRUCTIONS:")
        print("=" * 40)
        print("1. First, show your REAL FACE to the camera")
        print("2. Then, hold up a MOBILE PHONE with a photo")
        print("3. Try a PRINTED PHOTO on paper")
        print("4. Press 'q' to quit, 's' to save screenshot")
        print("5. Press 'r' to reset and test again")
        print("\n🚀 Starting real-time testing...")
        
        frame_history = []
        test_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ Error: Could not read frame")
                break
            
            # Convert BGR to RGB for processing
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Detect faces
            faces = detect_faces(rgb_frame)
            
            # Process each face
            for face in faces:
                x, y, w, h = face['bbox']
                
                # Draw face rectangle
                cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # Maintain frame history for motion analysis
                frame_history.append(rgb_frame.copy())
                if len(frame_history) > 10:
                    frame_history.pop(0)
                
                # Perform liveness check
                liveness_result = comprehensive_liveness_check(
                    rgb_frame, 
                    [x, y, w, h], 
                    frame_history if len(frame_history) >= 5 else None
                )
                
                # Display results on frame
                is_live = liveness_result['is_live']
                confidence = liveness_result['confidence']
                reason = liveness_result['reason']
                
                # Choose color based on result
                color = (0, 255, 0) if is_live else (0, 0, 255)  # Green for live, red for fake
                status = "LIVE PERSON" if is_live else "SPOOFING DETECTED"
                
                # Draw status text
                cv2.putText(frame, status, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)
                cv2.putText(frame, f"Confidence: {confidence:.2f}", (x, y + h + 20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
                
                # Display detailed information in terminal
                print(f"\n📊 REAL-TIME TEST #{test_count + 1}")
                print("-" * 30)
                print(f"🎯 Status: {status}")
                print(f"📈 Confidence: {confidence:.3f}")
                print(f"💭 Reason: {reason}")
                
                if 'details' in liveness_result:
                    details = liveness_result['details']
                    print(f"📋 Details:")
                    print(f"   • Quality Score: {details.get('quality_score', 'N/A'):.3f}")
                    print(f"   • Texture Score: {details.get('texture_score', 'N/A'):.3f}")
                    print(f"   • 3D Confidence: {details.get('face_3d_confidence', 'N/A'):.3f}")
                    print(f"   • Motion Detected: {details.get('motion_detected', 'N/A')}")
                    print(f"   • Spoofing Detected: {details.get('spoofing_detected', 'N/A')}")
                    print(f"   • Face Size: {details.get('face_size', 'N/A')}px")
                
                test_count += 1
            
            # Add instructions overlay
            cv2.putText(frame, "Real Camera Liveness Test", (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
            cv2.putText(frame, "q=quit, s=screenshot, r=reset", (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            cv2.putText(frame, f"Tests: {test_count}", (10, 90), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            
            # Show frame
            cv2.imshow('Real Camera Liveness Test', frame)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save screenshot
                timestamp = int(time.time())
                filename = f"liveness_test_{timestamp}.jpg"
                cv2.imwrite(filename, frame)
                print(f"📸 Screenshot saved: {filename}")
            elif key == ord('r'):
                # Reset test count
                test_count = 0
                frame_history = []
                print(f"🔄 Test counter reset")
        
        # Cleanup
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"\n📊 REAL CAMERA TEST SUMMARY:")
        print("=" * 40)
        print(f"✅ Total tests performed: {test_count}")
        print(f"🎥 Camera testing completed successfully")
        print(f"💡 Review the terminal output for detailed results")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_specific_scenarios():
    """Guide user through specific testing scenarios."""
    print(f"\n🎯 SPECIFIC TESTING SCENARIOS")
    print("=" * 50)
    print(f"Please test these scenarios in order:")
    print(f"")
    print(f"1. 👤 REAL FACE TEST:")
    print(f"   • Look directly at camera")
    print(f"   • Move slightly (natural movement)")
    print(f"   • Expected: GREEN box, 'LIVE PERSON'")
    print(f"")
    print(f"2. 📱 MOBILE PHONE PHOTO TEST:")
    print(f"   • Hold phone with your photo")
    print(f"   • Try different angles")
    print(f"   • Expected: RED box, 'SPOOFING DETECTED'")
    print(f"")
    print(f"3. 📱 MOBILE PHONE VIDEO TEST:")
    print(f"   • Play video of yourself on phone")
    print(f"   • Hold phone steady")
    print(f"   • Expected: RED box, 'SPOOFING DETECTED'")
    print(f"")
    print(f"4. 📄 PRINTED PHOTO TEST:")
    print(f"   • Hold printed photo of yourself")
    print(f"   • Try different lighting")
    print(f"   • Expected: RED box, 'SPOOFING DETECTED'")
    print(f"")
    print(f"5. 💻 LAPTOP SCREEN TEST:")
    print(f"   • Display your photo on laptop")
    print(f"   • Hold laptop towards camera")
    print(f"   • Expected: RED box, 'SPOOFING DETECTED'")

if __name__ == "__main__":
    print("🚀 REAL CAMERA LIVENESS DETECTION TEST")
    print("=" * 60)
    print("Testing liveness detection with actual camera in real environment")
    
    # Show testing scenarios
    test_specific_scenarios()
    
    input("\n⏸️  Press Enter when ready to start camera testing...")
    
    success = test_real_camera_liveness()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 REAL CAMERA TESTING COMPLETED!")
        print("✅ You have verified the system with actual camera")
        print("📊 Check the terminal output for detailed results")
        print("💡 The system is ready for real-world deployment!")
    else:
        print("❌ REAL CAMERA TESTING FAILED")
        print("🔧 Please check camera connection and try again")
    
    print("\n💡 NEXT STEPS:")
    print("• If real faces were blocked: Lower thresholds in .env file")
    print("• If spoofing was allowed: Increase thresholds in .env file")
    print("• Test with multiple people and lighting conditions")
    print("• Deploy to production once satisfied with results")
